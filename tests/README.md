# Indian Stock Market Trading Bot - Comprehensive Test Suite

This directory contains comprehensive test scenarios for validating the Indian stock market trading bot's signal processing capabilities, concurrent execution, and system stability.

## 🎯 Test Overview

The test suite validates:
- **All 5 Signal Formats**: BUY, INTIMATION, CLOSE, UPDATE, and CROSSOVER signals
- **NIFTY & SENSEX Support**: Both option chains with correct lot sizes (NIFTY: 75, SENSEX: 20)
- **Concurrent Execution**: Multiple simultaneous trades without conflicts
- **Sequential Processing**: Complete trade lifecycles with realistic timing
- **Error Handling**: Graceful handling of invalid signals and edge cases
- **Performance**: System stability under load

## 📁 Test Files

### Core Test Modules

1. **`comprehensive_trading_test.py`**
   - Signal parsing validation for all 5 formats
   - Instrument lookup testing (NIFTY/SENSEX)
   - Lot size calculation validation
   - CSV logging verification
   - Error handling testing

2. **`concurrent_execution_test.py`**
   - Concurrent BUY signal processing
   - Mixed signal burst testing
   - Database lock resilience testing
   - Race condition detection

3. **`sequential_trading_test.py`**
   - Complete trade lifecycle testing
   - NIFTY PUT sequence (your provided messages)
   - SENSEX CALL sequence
   - Mixed concurrent trades validation

4. **`test_your_messages.py`**
   - Quick test for your specific NIFTY PUT message sequence
   - Detailed message-by-message analysis
   - Trade lifecycle validation

5. **`run_all_tests.py`**
   - Master test runner
   - Comprehensive reporting
   - System readiness assessment

## 🚀 How to Run Tests

### Quick Test (Your Messages Only)
```bash
cd /Users/<USER>/sk/tele_client
python tests/test_your_messages.py
```

### Individual Test Suites
```bash
# Comprehensive tests (signal parsing, instrument lookup)
python tests/comprehensive_trading_test.py

# Concurrent execution tests
python tests/concurrent_execution_test.py

# Sequential trading tests
python tests/sequential_trading_test.py
```

### Complete Test Suite
```bash
# Run all tests with comprehensive reporting
python tests/run_all_tests.py
```

## 📊 Test Scenarios

### Format 1 - BUY Signals
```
===Algo_Trading===$TRADE$BUY$SENSEX 18 SEP 81500 PUT$152.9$146.3$2025-09-12 14:33:00+05:30
===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 PUT$30.6$28.6$2025-09-12 14:48:00+05:30
```

### Format 2 - INTIMATION/Hold Messages
```
===Algo_Trading===$INTIMATION$Continue to hold trade for $SENSEX 18 SEP 81500 PUT at current price: 151.2 (Entry price: 152.9) (Stop loss: 146.3)
```

### Format 3 - CLOSE Signals with Exit Reasons
```
===Algo_Trading===$TRADE$CLOSE$SENSEX 18 SEP 81500 PUT$157.4$2025-09-12 14:35:00+05:30$stop_loss_exit$157.4
```

### Format 4 - Stop Loss Updates
```
===Algo_Trading===$Update$STOP LOSS to$154.08$for option$SENSEX 18 SEP 81500 PUT current price: 157.8 (Entry price: 155.3)
```

### Format 5 - Crossover Exit Signals
```
===Algo_Trading===$TRADE$CLOSE$NIFTY 16 SEP 24700 PUT$82.35$2025-09-08 15:15:00+05:30$opposite_crossover_exit
```

## 🔍 Your Specific Test Sequence

The `test_your_messages.py` script tests your exact NIFTY PUT trading sequence:

1. **BUY**: NIFTY 16 SEP 25000 PUT @ 30.6 (SL: 28.6)
2. **INTIMATION**: Hold at 31.75 (SL: 30.16)
3. **UPDATE**: Stop loss to 30.25
4. **INTIMATION**: Hold at 31.85 (SL: 30.25)
5. **INTIMATION**: Hold at 32.0 (SL: 30.25)
6. **UPDATE**: Stop loss to 30.27
7. **INTIMATION**: Hold at 32.4 (SL: 30.27)
8. **INTIMATION**: Hold at 32.15 (SL: 30.27)
9. **UPDATE**: Stop loss to 30.33
10. **INTIMATION**: Hold at 31.25 (SL: 30.33)
11. **INTIMATION**: Hold at 30.5 (SL: 30.33)
12. **CLOSE**: Exit at 30.05 (stop_loss_exit)
13. **TRADE_LOG**: Final P&L summary

## 📈 Expected Results

### Lot Size Validation
- **NIFTY Options**: 1 lot = 75 units
- **SENSEX Options**: 1 lot = 20 units

### Success Criteria
- **Signal Parsing**: >95% success rate
- **Concurrent Processing**: No database locks or race conditions
- **Sequential Processing**: Complete trade lifecycle handling
- **Error Handling**: Graceful failure without crashes

### Performance Benchmarks
- **Signal Processing**: >1 signal/second
- **Concurrent Load**: Handle 20+ simultaneous signals
- **Memory Usage**: Stable without leaks
- **Database Operations**: No lock conflicts

## 📄 Test Reports

All tests generate detailed reports:

- **Log Files**: `tests/*.log` - Detailed execution logs
- **JSON Reports**: `tests/*_results.json` - Structured test data
- **Master Report**: `tests/reports/master_test_report_*.json` - Comprehensive analysis

## 🎯 System Readiness Levels

### PRODUCTION_READY ✅
- All tests pass with >95% success rate
- No critical issues
- Excellent performance metrics

### READY_WITH_MONITORING ⚠️
- Tests pass with >85% success rate
- Minor issues present
- Good performance with monitoring needed

### NEEDS_IMPROVEMENT ❌
- Tests pass with >75% success rate
- Some critical issues
- Performance optimization required

### NOT_READY 🚫
- Tests fail or <75% success rate
- Major critical issues
- Significant fixes needed

## 🛠️ Troubleshooting

### Common Issues

1. **Database Lock Errors**
   - Kill existing bot processes: `ps aux | grep main_new.py`
   - Use: `kill <PID>` to terminate

2. **Import Errors**
   - Ensure you're in the correct directory: `/Users/<USER>/sk/tele_client`
   - Check Python path includes parent directory

3. **API Connection Issues**
   - Verify SmartAPI credentials
   - Check network connectivity
   - Ensure market data access

4. **Instrument Lookup Failures**
   - Verify `Dependencies/all_instrument 2025-09-14.csv` exists
   - Check instrument data format
   - Validate expiry dates

### Debug Mode
Add `--debug` flag to any test for verbose logging:
```bash
python tests/test_your_messages.py --debug
```

## 📞 Support

For issues or questions:
1. Check log files for detailed error messages
2. Review test reports for specific failure points
3. Ensure all dependencies are properly installed
4. Verify trading bot configuration

## 🎉 Success Indicators

Your system is ready when:
- ✅ All 5 signal formats parse correctly
- ✅ NIFTY and SENSEX lot sizes are accurate
- ✅ Concurrent execution is stable
- ✅ Complete trade lifecycles work
- ✅ Error handling is robust
- ✅ Performance meets benchmarks

Run the tests and achieve PRODUCTION_READY status for confident live trading! 🚀
