{"total_signals": 13, "successful_signals": 0, "failed_signals": 13, "success_rate": 0.0, "total_processing_time": 0.0002701282501220703, "average_signal_time": 2.077909616323618e-05, "signal_results": [{"signal_number": 1, "signal_text": "===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 PUT$30.6$28.6$2025-09-12 14:48:00+05:30", "parsed": false, "error": "'SignalHandler' object has no attribute 'parse_message'", "processing_time": 1.1920928955078125e-05, "timestamp": "2025-09-14T12:24:46.994985"}, {"signal_number": 2, "signal_text": "===Algo_Trading===$INTIMATION$Continue to hold trade for $NIFTY 16 SEP 25000 PUT at current price: 31.75 (Entry price: 30.6) (Stop loss: 30.16)", "parsed": false, "error": "'SignalHandler' object has no attribute 'parse_message'", "processing_time": 4.291534423828125e-06, "timestamp": "2025-09-14T12:24:46.995010"}, {"signal_number": 3, "signal_text": "===Algo_Trading===$Update$STOP LOSS to$30.25$for option$NIFTY 16 SEP 25000 PUT current price: 31.85 (Entry price: 30.6)", "parsed": false, "error": "'SignalHandler' object has no attribute 'parse_message'", "processing_time": 3.0994415283203125e-06, "timestamp": "2025-09-14T12:24:46.995018"}, {"signal_number": 4, "signal_text": "===Algo_Trading===$INTIMATION$Continue to hold trade for $NIFTY 16 SEP 25000 PUT at current price: 31.85 (Entry price: 30.6) (Stop loss: 30.25)", "parsed": false, "error": "'SignalHandler' object has no attribute 'parse_message'", "processing_time": 2.86102294921875e-06, "timestamp": "2025-09-14T12:24:46.995026"}, {"signal_number": 5, "signal_text": "===Algo_Trading===$INTIMATION$Continue to hold trade for $NIFTY 16 SEP 25000 PUT at current price: 32.0 (Entry price: 30.6) (Stop loss: 30.25)", "parsed": false, "error": "'SignalHandler' object has no attribute 'parse_message'", "processing_time": 3.0994415283203125e-06, "timestamp": "2025-09-14T12:24:46.995033"}, {"signal_number": 6, "signal_text": "===Algo_Trading===$Update$STOP LOSS to$30.27$for option$NIFTY 16 SEP 25000 PUT current price: 32.4 (Entry price: 30.6)", "parsed": false, "error": "'SignalHandler' object has no attribute 'parse_message'", "processing_time": 3.0994415283203125e-06, "timestamp": "2025-09-14T12:24:46.995040"}, {"signal_number": 7, "signal_text": "===Algo_Trading===$INTIMATION$Continue to hold trade for $NIFTY 16 SEP 25000 PUT at current price: 32.4 (Entry price: 30.6) (Stop loss: 30.27)", "parsed": false, "error": "'SignalHandler' object has no attribute 'parse_message'", "processing_time": 2.86102294921875e-06, "timestamp": "2025-09-14T12:24:46.995047"}, {"signal_number": 8, "signal_text": "===Algo_Trading===$INTIMATION$Continue to hold trade for $NIFTY 16 SEP 25000 PUT at current price: 32.15 (Entry price: 30.6) (Stop loss: 30.27)", "parsed": false, "error": "'SignalHandler' object has no attribute 'parse_message'", "processing_time": 4.0531158447265625e-06, "timestamp": "2025-09-14T12:24:46.995067"}, {"signal_number": 9, "signal_text": "===Algo_Trading===$Update$STOP LOSS to$30.33$for option$NIFTY 16 SEP 25000 PUT current price: 31.25 (Entry price: 30.6)", "parsed": false, "error": "'SignalHandler' object has no attribute 'parse_message'", "processing_time": 3.0994415283203125e-06, "timestamp": "2025-09-14T12:24:46.995075"}, {"signal_number": 10, "signal_text": "===Algo_Trading===$INTIMATION$Continue to hold trade for $NIFTY 16 SEP 25000 PUT at current price: 31.25 (Entry price: 30.6) (Stop loss: 30.33)", "parsed": false, "error": "'SignalHandler' object has no attribute 'parse_message'", "processing_time": 3.0994415283203125e-06, "timestamp": "2025-09-14T12:24:46.995082"}, {"signal_number": 11, "signal_text": "===Algo_Trading===$INTIMATION$Continue to hold trade for $NIFTY 16 SEP 25000 PUT at current price: 30.5 (Entry price: 30.6) (Stop loss: 30.33)", "parsed": false, "error": "'SignalHandler' object has no attribute 'parse_message'", "processing_time": 2.6226043701171875e-06, "timestamp": "2025-09-14T12:24:46.995089"}, {"signal_number": 12, "signal_text": "===Algo_Trading===$TRADE$CLOSE$NIFTY 16 SEP 25000 PUT$30.05$2025-09-12 15:00:00+05:30$stop_loss_exit$30.33", "parsed": false, "error": "'SignalHandler' object has no attribute 'parse_message'", "processing_time": 1.9073486328125e-06, "timestamp": "2025-09-14T12:24:46.995095"}, {"signal_number": 13, "signal_text": "===Algo_Trading===$INTIMATION$Trade_log$Instrument_name        trade_entry_time          trade_exit_time            Trade_entry_price Trade_exit_price profit_loss\nNIFTY 16 SEP 25000 PUT 2025-09-12 14:48:00+05:30 2025-09-12 15:00:00+05:30 30.6                30.33           -80.25", "parsed": false, "error": "'SignalHandler' object has no attribute 'parse_message'", "processing_time": 1.1205673217773438e-05, "timestamp": "2025-09-14T12:24:46.995219"}]}