#!/usr/bin/env python3
"""
Master Test Runner for Indian Stock Market Trading Bot
Runs all comprehensive test scenarios including:
1. Signal parsing validation (all 5 formats)
2. Concurrent execution testing
3. Sequential trade lifecycle testing
4. NIFTY and SENSEX support validation
5. System stability and performance testing
"""

import os
import sys
import time
import json
import logging
from datetime import datetime
from typing import Dict, Any

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import all test modules
from tests.comprehensive_trading_test import ComprehensiveTradingTest
from tests.concurrent_execution_test import ConcurrentExecutionTest
from tests.sequential_trading_test import SequentialTradingTest

# Configure master logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('tests/master_test_results.log'),
        logging.StreamHandler()
    ]
)

class MasterTestRunner:
    """
    Master test runner that orchestrates all trading bot tests
    """
    
    def __init__(self):
        self.logger = logging.getLogger('MasterTestRunner')
        self.test_results = {}
        self.start_time = None
        self.end_time = None
        
        # Create test directories
        os.makedirs('tests', exist_ok=True)
        os.makedirs('tests/reports', exist_ok=True)
        
    def run_comprehensive_tests(self) -> Dict[str, Any]:
        """Run comprehensive trading tests (signal parsing, instrument lookup, etc.)"""
        self.logger.info("🧪 Running Comprehensive Trading Tests...")
        
        try:
            test_suite = ComprehensiveTradingTest()
            results = test_suite.run_all_tests()
            
            self.logger.info("✅ Comprehensive tests completed successfully")
            return {
                "status": "completed",
                "results": results,
                "test_type": "comprehensive"
            }
            
        except Exception as e:
            self.logger.error(f"❌ Comprehensive tests failed: {e}")
            return {
                "status": "failed",
                "error": str(e),
                "test_type": "comprehensive"
            }
    
    def run_concurrent_tests(self) -> Dict[str, Any]:
        """Run concurrent execution tests"""
        self.logger.info("🚀 Running Concurrent Execution Tests...")
        
        try:
            test_suite = ConcurrentExecutionTest()
            results = test_suite.run_all_concurrent_tests()
            
            self.logger.info("✅ Concurrent tests completed successfully")
            return {
                "status": "completed",
                "results": results,
                "test_type": "concurrent"
            }
            
        except Exception as e:
            self.logger.error(f"❌ Concurrent tests failed: {e}")
            return {
                "status": "failed",
                "error": str(e),
                "test_type": "concurrent"
            }
    
    def run_sequential_tests(self) -> Dict[str, Any]:
        """Run sequential trading lifecycle tests"""
        self.logger.info("🎯 Running Sequential Trading Tests...")
        
        try:
            test_suite = SequentialTradingTest()
            results = test_suite.run_all_sequential_tests()
            
            self.logger.info("✅ Sequential tests completed successfully")
            return {
                "status": "completed",
                "results": results,
                "test_type": "sequential"
            }
            
        except Exception as e:
            self.logger.error(f"❌ Sequential tests failed: {e}")
            return {
                "status": "failed",
                "error": str(e),
                "test_type": "sequential"
            }
    
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all test suites in sequence"""
        self.logger.info("🎯 Starting Master Test Suite for Trading Bot")
        self.logger.info("=" * 60)
        
        self.start_time = datetime.now()
        
        # Run all test suites
        self.test_results = {
            "test_metadata": {
                "start_time": self.start_time.isoformat(),
                "test_environment": "development",
                "bot_version": "1.0.0"
            },
            "comprehensive_tests": self.run_comprehensive_tests(),
            "concurrent_tests": self.run_concurrent_tests(),
            "sequential_tests": self.run_sequential_tests()
        }
        
        self.end_time = datetime.now()
        self.test_results["test_metadata"]["end_time"] = self.end_time.isoformat()
        self.test_results["test_metadata"]["total_duration"] = str(self.end_time - self.start_time)
        
        # Generate master summary
        self.test_results["master_summary"] = self.generate_master_summary()
        
        return self.test_results
    
    def generate_master_summary(self) -> Dict[str, Any]:
        """Generate comprehensive summary across all test suites"""
        summary = {
            "overall_status": "UNKNOWN",
            "test_suites_run": 0,
            "test_suites_passed": 0,
            "test_suites_failed": 0,
            "total_signals_tested": 0,
            "total_successful_signals": 0,
            "overall_success_rate": 0,
            "critical_issues": [],
            "warnings": [],
            "performance_metrics": {},
            "recommendations": [],
            "system_readiness": "UNKNOWN"
        }
        
        # Analyze each test suite
        for test_name, test_result in self.test_results.items():
            if test_name == "test_metadata" or test_name == "master_summary":
                continue
                
            summary["test_suites_run"] += 1
            
            if test_result.get("status") == "completed":
                summary["test_suites_passed"] += 1
                
                # Extract metrics from each test type
                if test_name == "comprehensive_tests":
                    comp_summary = test_result["results"].get("summary", {})
                    summary["total_signals_tested"] += comp_summary.get("total_tests_run", 0)
                    summary["total_successful_signals"] += comp_summary.get("tests_passed", 0)
                    summary["critical_issues"].extend(comp_summary.get("critical_issues", []))
                    summary["warnings"].extend(comp_summary.get("warnings", []))
                
                elif test_name == "concurrent_tests":
                    conc_summary = test_result["results"].get("summary", {})
                    summary["total_signals_tested"] += conc_summary.get("total_signals_processed", 0)
                    summary["total_successful_signals"] += conc_summary.get("total_successful", 0)
                    
                    # Add concurrent-specific issues
                    if conc_summary.get("system_stability") != "STABLE":
                        summary["critical_issues"].append(f"Concurrent execution stability: {conc_summary.get('system_stability')}")
                
                elif test_name == "sequential_tests":
                    seq_summary = test_result["results"].get("summary", {})
                    summary["total_signals_tested"] += seq_summary.get("total_signals_processed", 0)
                    summary["total_successful_signals"] += seq_summary.get("total_successful_signals", 0)
                    summary["critical_issues"].extend(seq_summary.get("critical_issues", []))
                    
                    # Add performance metrics
                    perf_metrics = seq_summary.get("performance_metrics", {})
                    summary["performance_metrics"]["signals_per_second"] = perf_metrics.get("signals_per_second", 0)
                    summary["performance_metrics"]["average_sequence_time"] = perf_metrics.get("average_sequence_time", 0)
            
            else:
                summary["test_suites_failed"] += 1
                summary["critical_issues"].append(f"{test_name} failed: {test_result.get('error', 'Unknown error')}")
        
        # Calculate overall success rate
        if summary["total_signals_tested"] > 0:
            summary["overall_success_rate"] = (summary["total_successful_signals"] / summary["total_signals_tested"]) * 100
        
        # Determine overall status
        if summary["test_suites_failed"] == 0 and summary["overall_success_rate"] >= 95:
            summary["overall_status"] = "EXCELLENT"
            summary["system_readiness"] = "PRODUCTION_READY"
        elif summary["test_suites_failed"] == 0 and summary["overall_success_rate"] >= 85:
            summary["overall_status"] = "GOOD"
            summary["system_readiness"] = "READY_WITH_MONITORING"
        elif summary["test_suites_failed"] <= 1 and summary["overall_success_rate"] >= 75:
            summary["overall_status"] = "ACCEPTABLE"
            summary["system_readiness"] = "NEEDS_IMPROVEMENT"
        else:
            summary["overall_status"] = "POOR"
            summary["system_readiness"] = "NOT_READY"
        
        # Generate recommendations
        if summary["overall_status"] == "EXCELLENT":
            summary["recommendations"].append("🎉 System is performing excellently and is ready for production deployment!")
            summary["recommendations"].append("✅ All test suites passed with high success rates")
            summary["recommendations"].append("🚀 Consider implementing additional monitoring for production environment")
        
        elif summary["overall_status"] == "GOOD":
            summary["recommendations"].append("✅ System is performing well with minor issues to address")
            summary["recommendations"].append("🔧 Review and fix the identified warnings before production deployment")
            summary["recommendations"].append("📊 Implement comprehensive monitoring and alerting")
        
        else:
            summary["recommendations"].append("❌ System has significant issues that must be addressed")
            summary["recommendations"].append("🛠️ Fix all critical issues before considering production deployment")
            summary["recommendations"].append("🧪 Re-run tests after fixes to validate improvements")
        
        # Add specific recommendations based on test results
        if len(summary["critical_issues"]) > 0:
            summary["recommendations"].append(f"🚨 Address {len(summary['critical_issues'])} critical issues immediately")
        
        if summary["performance_metrics"].get("signals_per_second", 0) < 1:
            summary["recommendations"].append("⚡ Performance optimization needed - signal processing is too slow")
        
        return summary
    
    def export_master_report(self) -> str:
        """Export comprehensive master test report"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"tests/reports/master_test_report_{timestamp}.json"
        
        # Create comprehensive report
        master_report = {
            "report_metadata": {
                "report_type": "Master Trading Bot Test Report",
                "generated_at": datetime.now().isoformat(),
                "test_duration": str(self.end_time - self.start_time) if self.end_time and self.start_time else "Unknown",
                "report_version": "1.0"
            },
            "executive_summary": {
                "overall_status": self.test_results["master_summary"]["overall_status"],
                "system_readiness": self.test_results["master_summary"]["system_readiness"],
                "success_rate": f"{self.test_results['master_summary']['overall_success_rate']:.1f}%",
                "critical_issues_count": len(self.test_results["master_summary"]["critical_issues"]),
                "test_suites_passed": f"{self.test_results['master_summary']['test_suites_passed']}/{self.test_results['master_summary']['test_suites_run']}"
            },
            "detailed_results": self.test_results,
            "test_coverage": {
                "signal_formats_tested": 5,  # All 5 signal formats
                "instruments_tested": ["NIFTY", "SENSEX"],
                "test_scenarios": ["parsing", "concurrent", "sequential", "error_handling", "performance"],
                "lot_size_validation": True,
                "market_timing_validation": True
            }
        }
        
        # Save report
        with open(report_file, 'w') as f:
            json.dump(master_report, f, indent=2, default=str)
        
        self.logger.info(f"📄 Master test report exported to: {report_file}")
        return report_file


def main():
    """Main execution function"""
    print("🎯 INDIAN STOCK MARKET TRADING BOT - MASTER TEST SUITE")
    print("=" * 70)
    print("Testing all signal formats, concurrent execution, and system stability")
    print("Validating NIFTY and SENSEX options with proper lot sizes")
    print("=" * 70)
    
    # Create master test runner
    runner = MasterTestRunner()
    
    # Run all tests
    results = runner.run_all_tests()
    
    # Print master summary
    summary = results["master_summary"]
    print(f"\n🏆 MASTER TEST SUMMARY")
    print(f"{'='*50}")
    print(f"Overall Status: {summary['overall_status']}")
    print(f"System Readiness: {summary['system_readiness']}")
    print(f"Test Suites: {summary['test_suites_passed']}/{summary['test_suites_run']} passed")
    print(f"Overall Success Rate: {summary['overall_success_rate']:.1f}%")
    print(f"Total Signals Tested: {summary['total_signals_tested']}")
    print(f"Successful Signals: {summary['total_successful_signals']}")
    
    # Print performance metrics
    if summary["performance_metrics"]:
        print(f"\n⚡ PERFORMANCE METRICS:")
        for metric, value in summary["performance_metrics"].items():
            print(f"  {metric.replace('_', ' ').title()}: {value:.3f}")
    
    # Print critical issues
    if summary["critical_issues"]:
        print(f"\n❌ CRITICAL ISSUES ({len(summary['critical_issues'])}):")
        for i, issue in enumerate(summary["critical_issues"][:5], 1):  # Show first 5
            print(f"  {i}. {issue}")
        if len(summary["critical_issues"]) > 5:
            print(f"  ... and {len(summary['critical_issues']) - 5} more issues")
    
    # Print recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    for rec in summary["recommendations"]:
        print(f"  • {rec}")
    
    # Export master report
    report_file = runner.export_master_report()
    
    print(f"\n📄 Comprehensive report saved to: {report_file}")
    print(f"🏁 Master test suite completed!")
    
    # Final status message
    if summary["system_readiness"] == "PRODUCTION_READY":
        print(f"\n🎉 CONGRATULATIONS! Your trading bot is ready for production! 🚀")
    elif summary["system_readiness"] == "READY_WITH_MONITORING":
        print(f"\n✅ Your trading bot is ready with some monitoring recommendations.")
    else:
        print(f"\n⚠️ Your trading bot needs improvements before production deployment.")
    
    return results


if __name__ == "__main__":
    main()
