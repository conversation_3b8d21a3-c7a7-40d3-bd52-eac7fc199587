{"total_signals": 13, "successful_signals": 12, "failed_signals": 1, "success_rate": 92.3076923076923, "total_processing_time": 8.809103965759277, "average_signal_time": 0.6776233819814829, "signal_results": [{"signal_number": 1, "signal_text": "===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 PUT$30.6$28.6$2025-09-12 14:48:00+05:30", "parsed": true, "symbol": "NIFTY", "action": "BUY", "signal_type": "TRADE", "processing_time": 1.5443649291992188, "dataframe_processed": true, "api_processed": true, "api_success": true, "timestamp": "2025-09-14T12:25:26.000311", "price": 30.6, "strike": 25000.0, "option_type": "PE"}, {"signal_number": 2, "signal_text": "===Algo_Trading===$INTIMATION$Continue to hold trade for $NIFTY 16 SEP 25000 PUT at current price: 31.75 (Entry price: 30.6) (Stop loss: 30.16)", "parsed": true, "symbol": "NIFTY", "action": "HOLD", "signal_type": "INTIMATION", "processing_time": 0.002574920654296875, "dataframe_processed": true, "api_processed": false, "api_success": null, "timestamp": "2025-09-14T12:25:26.508054", "current_price": 31.75, "entry_price": 30.6, "stop_loss": 30.16}, {"signal_number": 3, "signal_text": "===Algo_Trading===$Update$STOP LOSS to$30.25$for option$NIFTY 16 SEP 25000 PUT current price: 31.85 (Entry price: 30.6)", "parsed": true, "symbol": "NIFTY", "action": "UPDATE", "signal_type": "UPDATE", "processing_time": 0.0019788742065429688, "dataframe_processed": true, "api_processed": false, "api_success": null, "timestamp": "2025-09-14T12:25:27.015277", "new_stop_loss": 30.25, "update_type": "STOP LOSS"}, {"signal_number": 4, "signal_text": "===Algo_Trading===$INTIMATION$Continue to hold trade for $NIFTY 16 SEP 25000 PUT at current price: 31.85 (Entry price: 30.6) (Stop loss: 30.25)", "parsed": true, "symbol": "NIFTY", "action": "HOLD", "signal_type": "INTIMATION", "processing_time": 0.001631021499633789, "dataframe_processed": true, "api_processed": false, "api_success": null, "timestamp": "2025-09-14T12:25:27.518014", "current_price": 31.85, "entry_price": 30.6, "stop_loss": 30.25}, {"signal_number": 5, "signal_text": "===Algo_Trading===$INTIMATION$Continue to hold trade for $NIFTY 16 SEP 25000 PUT at current price: 32.0 (Entry price: 30.6) (Stop loss: 30.25)", "parsed": true, "symbol": "NIFTY", "action": "HOLD", "signal_type": "INTIMATION", "processing_time": 0.0014090538024902344, "dataframe_processed": true, "api_processed": false, "api_success": null, "timestamp": "2025-09-14T12:25:28.024628", "current_price": 32.0, "entry_price": 30.6, "stop_loss": 30.25}, {"signal_number": 6, "signal_text": "===Algo_Trading===$Update$STOP LOSS to$30.27$for option$NIFTY 16 SEP 25000 PUT current price: 32.4 (Entry price: 30.6)", "parsed": true, "symbol": "NIFTY", "action": "UPDATE", "signal_type": "UPDATE", "processing_time": 0.0012011528015136719, "dataframe_processed": true, "api_processed": false, "api_success": null, "timestamp": "2025-09-14T12:25:28.526024", "new_stop_loss": 30.27, "update_type": "STOP LOSS"}, {"signal_number": 7, "signal_text": "===Algo_Trading===$INTIMATION$Continue to hold trade for $NIFTY 16 SEP 25000 PUT at current price: 32.4 (Entry price: 30.6) (Stop loss: 30.27)", "parsed": true, "symbol": "NIFTY", "action": "HOLD", "signal_type": "INTIMATION", "processing_time": 0.0016901493072509766, "dataframe_processed": true, "api_processed": false, "api_success": null, "timestamp": "2025-09-14T12:25:29.031723", "current_price": 32.4, "entry_price": 30.6, "stop_loss": 30.27}, {"signal_number": 8, "signal_text": "===Algo_Trading===$INTIMATION$Continue to hold trade for $NIFTY 16 SEP 25000 PUT at current price: 32.15 (Entry price: 30.6) (Stop loss: 30.27)", "parsed": true, "symbol": "NIFTY", "action": "HOLD", "signal_type": "INTIMATION", "processing_time": 0.0008752346038818359, "dataframe_processed": true, "api_processed": false, "api_success": null, "timestamp": "2025-09-14T12:25:29.535771", "current_price": 32.15, "entry_price": 30.6, "stop_loss": 30.27}, {"signal_number": 9, "signal_text": "===Algo_Trading===$Update$STOP LOSS to$30.33$for option$NIFTY 16 SEP 25000 PUT current price: 31.25 (Entry price: 30.6)", "parsed": true, "symbol": "NIFTY", "action": "UPDATE", "signal_type": "UPDATE", "processing_time": 0.0017731189727783203, "dataframe_processed": true, "api_processed": false, "api_success": null, "timestamp": "2025-09-14T12:25:30.042671", "new_stop_loss": 30.33, "update_type": "STOP LOSS"}, {"signal_number": 10, "signal_text": "===Algo_Trading===$INTIMATION$Continue to hold trade for $NIFTY 16 SEP 25000 PUT at current price: 31.25 (Entry price: 30.6) (Stop loss: 30.33)", "parsed": true, "symbol": "NIFTY", "action": "HOLD", "signal_type": "INTIMATION", "processing_time": 0.002485990524291992, "dataframe_processed": true, "api_processed": false, "api_success": null, "timestamp": "2025-09-14T12:25:30.550246", "current_price": 31.25, "entry_price": 30.6, "stop_loss": 30.33}, {"signal_number": 11, "signal_text": "===Algo_Trading===$INTIMATION$Continue to hold trade for $NIFTY 16 SEP 25000 PUT at current price: 30.5 (Entry price: 30.6) (Stop loss: 30.33)", "parsed": true, "symbol": "NIFTY", "action": "HOLD", "signal_type": "INTIMATION", "processing_time": 0.0011267662048339844, "dataframe_processed": true, "api_processed": false, "api_success": null, "timestamp": "2025-09-14T12:25:31.056513", "current_price": 30.5, "entry_price": 30.6, "stop_loss": 30.33}, {"signal_number": 12, "signal_text": "===Algo_Trading===$TRADE$CLOSE$NIFTY 16 SEP 25000 PUT$30.05$2025-09-12 15:00:00+05:30$stop_loss_exit$30.33", "parsed": true, "symbol": "NIFTY", "action": "CLOSE", "signal_type": "TRADE", "processing_time": 1.199739933013916, "dataframe_processed": true, "api_processed": true, "api_success": true, "timestamp": "2025-09-14T12:25:32.761377", "price": 30.33, "strike": 25000.0, "option_type": "PE"}, {"signal_number": 13, "signal_text": "===Algo_Trading===$INTIMATION$Trade_log$Instrument_name        trade_entry_time          trade_exit_time            Trade_entry_price Trade_exit_price profit_loss\nNIFTY 16 SEP 25000 PUT 2025-09-12 14:48:00+05:30 2025-09-12 15:00:00+05:30 30.6                30.33           -80.25", "parsed": false, "error": "Failed to parse signal", "processing_time": 0.0005700588226318359, "timestamp": "2025-09-14T12:25:33.265016"}]}