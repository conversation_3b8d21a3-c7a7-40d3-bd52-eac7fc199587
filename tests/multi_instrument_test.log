2025-09-14 12:32:36,769 - api - INFO - SmartAPI client initialized successfully
2025-09-14 12:32:36,964 - api - INFO - Loaded 127276 instruments from Dependencies/all_instrument 2025-09-14.csv
2025-09-14 12:32:37,034 - market_data - INFO - Market Data Manager initialized
2025-09-14 12:32:37,035 - instrument_updater - INFO - Instrument Updater initialized
2025-09-14 12:32:37,035 - instrument_updater - INFO - Today's instrument file already exists
2025-09-14 12:32:37,035 - instrument_updater - INFO - Instrument update not needed
2025-09-14 12:32:37,035 - trading_dataframes - INFO - Instrument files updated successfully
2025-09-14 12:32:37,201 - api - INFO - Loaded 127276 instruments from Dependencies/all_instrument 2025-09-14.csv
2025-09-14 12:32:37,207 - trading_dataframes - INFO - Reloaded instruments in API handler
2025-09-14 12:32:37,207 - trading_dataframes - INFO - Trading Dataframe Manager initialized with market data integration
2025-09-14 12:32:37,207 - MultiInstrumentTest - INFO - 🚀 Testing 5 concurrent signals per instrument (NIFTY & SENSEX)...
2025-09-14 12:32:37,208 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25100 PUT$75.5$70.8$2025-09-12 14:34:00+05:30
2025-09-14 12:32:37,208 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:00+05:30
2025-09-14 12:32:37,208 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25200 CALL$145.2$138.5$2025-09-12 14:35:00+05:30
2025-09-14 12:32:37,209 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25100.0, option_type='PE', timestamp=datetime.datetime(2025, 9, 12, 14, 34, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=75.5, stop_loss=70.8, entry_price=75.5, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:32:37,209 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 24900 PUT$85.3$80.1$2025-09-12 14:36:00+05:30
2025-09-14 12:32:37,209 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=165.8, stop_loss=160.2, entry_price=165.8, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:32:37,209 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25200.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 35, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=145.2, stop_loss=138.5, entry_price=145.2, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:32:37,211 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:32:37,210 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$SENSEX 18 SEP 81500 CALL$175.4$168.2$2025-09-12 14:34:00+05:30
2025-09-14 12:32:37,210 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$SENSEX 18 SEP 82000 PUT$152.9$146.3$2025-09-12 14:33:00+05:30
2025-09-14 12:32:37,211 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:32:37,210 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25300 CALL$125.7$119.8$2025-09-12 14:37:00+05:30
2025-09-14 12:32:37,210 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$SENSEX 18 SEP 82500 PUT$135.6$129.8$2025-09-12 14:35:00+05:30
2025-09-14 12:32:37,211 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:32:37,212 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252510000PE
2025-09-14 12:32:37,211 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$SENSEX 18 SEP 81000 CALL$195.3$187.1$2025-09-12 14:36:00+05:30
2025-09-14 12:32:37,211 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$SENSEX 18 SEP 83000 PUT$115.8$110.9$2025-09-12 14:37:00+05:30
2025-09-14 12:32:37,213 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 12:32:37,211 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=24900.0, option_type='PE', timestamp=datetime.datetime(2025, 9, 12, 14, 36, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=85.3, stop_loss=80.1, entry_price=85.3, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:32:37,212 - trade - INFO - Parsed signal: TradeSignal(symbol='SENSEX', action='BUY', strike=81500.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 34, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=175.4, stop_loss=168.2, entry_price=175.4, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='18 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:32:37,214 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252520000CE
2025-09-14 12:32:37,213 - trade - INFO - Parsed signal: TradeSignal(symbol='SENSEX', action='BUY', strike=82000.0, option_type='PE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=152.9, stop_loss=146.3, entry_price=152.9, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='18 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:32:37,214 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25300.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 37, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=125.7, stop_loss=119.8, entry_price=125.7, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:32:37,214 - trade - INFO - Parsed signal: TradeSignal(symbol='SENSEX', action='BUY', strike=82500.0, option_type='PE', timestamp=datetime.datetime(2025, 9, 12, 14, 35, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=135.6, stop_loss=129.8, entry_price=135.6, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='18 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:32:37,221 - trade - INFO - Parsed signal: TradeSignal(symbol='SENSEX', action='BUY', strike=81000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 36, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=195.3, stop_loss=187.1, entry_price=195.3, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='18 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:32:37,227 - trade - INFO - Parsed signal: TradeSignal(symbol='SENSEX', action='BUY', strike=83000.0, option_type='PE', timestamp=datetime.datetime(2025, 9, 12, 14, 37, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=115.8, stop_loss=110.9, entry_price=115.8, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='18 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:32:37,245 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:32:37,250 - trading_dataframes - INFO - Processing signal: TRADE - BUY for SENSEX
2025-09-14 12:32:37,286 - trading_dataframes - INFO - Processing signal: TRADE - BUY for SENSEX
2025-09-14 12:32:37,296 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:32:37,308 - market_data - INFO - Market is closed, fetching historical price for NIFTY
2025-09-14 12:32:37,320 - market_data - INFO - Market is closed, fetching historical price for NIFTY
2025-09-14 12:32:37,308 - trading_dataframes - INFO - Processing signal: TRADE - BUY for SENSEX
2025-09-14 12:32:37,333 - trading_dataframes - INFO - Processing signal: TRADE - BUY for SENSEX
2025-09-14 12:32:37,337 - trading_dataframes - INFO - Processing signal: TRADE - BUY for SENSEX
2025-09-14 12:32:37,338 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252490000PE
2025-09-14 12:32:37,337 - market_data - INFO - Market is closed, fetching historical price for NIFTY
2025-09-14 12:32:37,338 - api - INFO - Looking for instrument with trading symbol: SENSEX18SEP20258150000CE
2025-09-14 12:32:37,339 - api - INFO - Looking for instrument with trading symbol: SENSEX18SEP20258200000PE
2025-09-14 12:32:37,339 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252530000CE
2025-09-14 12:32:37,339 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-12
2025-09-14 12:32:37,339 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-12
2025-09-14 12:32:37,340 - api - INFO - Looking for instrument with trading symbol: SENSEX18SEP20258250000PE
2025-09-14 12:32:37,340 - api - INFO - Looking for instrument with trading symbol: SENSEX18SEP20258100000CE
2025-09-14 12:32:37,341 - api - INFO - Looking for instrument with trading symbol: SENSEX18SEP20258300000PE
2025-09-14 12:32:37,356 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-12
2025-09-14 12:32:37,461 - market_data - INFO - Market is closed, fetching historical price for NIFTY
2025-09-14 12:32:37,483 - market_data - INFO - Market is closed, fetching historical price for SENSEX
2025-09-14 12:32:37,537 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-12
2025-09-14 12:32:37,561 - market_data - INFO - Market is closed, fetching historical price for SENSEX
2025-09-14 12:32:37,593 - market_data - INFO - Market is closed, fetching historical price for SENSEX
2025-09-14 12:32:37,597 - market_data - INFO - Market is closed, fetching historical price for NIFTY
2025-09-14 12:32:37,611 - market_data - INFO - Market is closed, fetching historical price for SENSEX
2025-09-14 12:32:37,611 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-12
2025-09-14 12:32:37,612 - market_data - INFO - Market is closed, fetching historical price for SENSEX
2025-09-14 12:32:37,612 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-12
2025-09-14 12:32:37,612 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-12
2025-09-14 12:32:37,613 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-12
2025-09-14 12:32:37,613 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-12
2025-09-14 12:32:37,614 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-12
2025-09-14 12:32:37,749 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-11
2025-09-14 12:32:37,956 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-11
2025-09-14 12:32:38,135 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-11
2025-09-14 12:32:38,200 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-11
2025-09-14 12:32:38,227 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-10
2025-09-14 12:32:38,229 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-11
2025-09-14 12:32:38,230 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-11
2025-09-14 12:32:38,232 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-11
2025-09-14 12:32:38,239 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-11
2025-09-14 12:32:38,242 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-10
2025-09-14 12:32:38,244 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-11
2025-09-14 12:32:38,248 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-11
2025-09-14 12:32:38,317 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-10
2025-09-14 12:32:38,360 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-10
2025-09-14 12:32:38,674 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-10
2025-09-14 12:32:38,705 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-09
2025-09-14 12:32:38,706 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-10
2025-09-14 12:32:38,714 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-10
2025-09-14 12:32:38,729 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-10
2025-09-14 12:32:38,731 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-09
2025-09-14 12:32:38,733 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-10
2025-09-14 12:32:38,737 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-10
2025-09-14 12:32:38,748 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-09
2025-09-14 12:32:38,750 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-09
2025-09-14 12:32:38,851 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-09
2025-09-14 12:32:39,002 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-08
2025-09-14 12:32:39,066 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-09
2025-09-14 12:32:39,123 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-09
2025-09-14 12:32:39,275 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-08
2025-09-14 12:32:39,280 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-09
2025-09-14 12:32:39,282 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-09
2025-09-14 12:32:39,311 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-09
2025-09-14 12:32:39,335 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-08
2025-09-14 12:32:39,335 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-08
2025-09-14 12:32:39,339 - market_data - WARNING - No historical price data available for NIFTY after trying multiple dates
2025-09-14 12:32:39,343 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-08
2025-09-14 12:32:39,344 - market_data - WARNING - No historical price available for NIFTY
2025-09-14 12:32:39,345 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:32:39,346 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:32:39,350 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 165.8
2025-09-14 12:32:39,350 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 12:32:39,350 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 12:32:39,395 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-08
2025-09-14 12:32:39,402 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-08
2025-09-14 12:32:39,470 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-08
2025-09-14 12:32:39,480 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-08
2025-09-14 12:32:39,508 - market_data - WARNING - No historical price data available for NIFTY after trying multiple dates
2025-09-14 12:32:39,508 - market_data - WARNING - No historical price available for NIFTY
2025-09-14 12:32:39,508 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:32:39,508 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:32:39,509 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25100 PUT at price 75.5
2025-09-14 12:32:39,509 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25100 PUT
2025-09-14 12:32:39,509 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252510000PE
2025-09-14 12:32:39,598 - market_data - WARNING - No historical price data available for SENSEX after trying multiple dates
2025-09-14 12:32:39,598 - market_data - WARNING - No historical price available for SENSEX
2025-09-14 12:32:39,598 - trading_dataframes - WARNING - Could not retrieve market price for SENSEX
2025-09-14 12:32:39,598 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:32:39,601 - market_data - WARNING - No historical price data available for SENSEX after trying multiple dates
2025-09-14 12:32:39,602 - trading_dataframes - INFO - Created new trade entry for SENSEX 18 SEP 82000 PUT at price 152.9
2025-09-14 12:32:39,603 - market_data - WARNING - No historical price available for SENSEX
2025-09-14 12:32:39,603 - trade - INFO - Dataframe updated: Trade entry created for SENSEX 18 SEP 82000 PUT
2025-09-14 12:32:39,603 - trading_dataframes - WARNING - Could not retrieve market price for SENSEX
2025-09-14 12:32:39,603 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:32:39,603 - api - INFO - Looking for instrument with trading symbol: SENSEX18SEP20258200000PE
2025-09-14 12:32:39,604 - trading_dataframes - INFO - Created new trade entry for SENSEX 18 SEP 82500 PUT at price 135.6
2025-09-14 12:32:39,610 - trade - INFO - Dataframe updated: Trade entry created for SENSEX 18 SEP 82500 PUT
2025-09-14 12:32:39,620 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-08
2025-09-14 12:32:39,610 - api - INFO - Looking for instrument with trading symbol: SENSEX18SEP20258250000PE
2025-09-14 12:32:39,652 - market_data - WARNING - No historical price data available for SENSEX after trying multiple dates
2025-09-14 12:32:39,690 - market_data - WARNING - No historical price available for SENSEX
2025-09-14 12:32:39,693 - market_data - WARNING - No historical price data available for SENSEX after trying multiple dates
2025-09-14 12:32:39,696 - trading_dataframes - WARNING - Could not retrieve market price for SENSEX
2025-09-14 12:32:39,696 - market_data - WARNING - No historical price data available for SENSEX after trying multiple dates
2025-09-14 12:32:39,696 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:32:39,696 - market_data - WARNING - No historical price available for SENSEX
2025-09-14 12:32:39,697 - market_data - WARNING - No historical price available for SENSEX
2025-09-14 12:32:39,698 - trading_dataframes - INFO - Created new trade entry for SENSEX 18 SEP 83000 PUT at price 115.8
2025-09-14 12:32:39,698 - trading_dataframes - WARNING - Could not retrieve market price for SENSEX
2025-09-14 12:32:39,698 - trading_dataframes - WARNING - Could not retrieve market price for SENSEX
2025-09-14 12:32:39,698 - trade - INFO - Dataframe updated: Trade entry created for SENSEX 18 SEP 83000 PUT
2025-09-14 12:32:39,698 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:32:39,698 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:32:39,699 - trading_dataframes - INFO - Created new trade entry for SENSEX 18 SEP 81000 CALL at price 195.3
2025-09-14 12:32:39,698 - api - INFO - Looking for instrument with trading symbol: SENSEX18SEP20258300000PE
2025-09-14 12:32:39,700 - trade - INFO - Dataframe updated: Trade entry created for SENSEX 18 SEP 81000 CALL
2025-09-14 12:32:39,700 - trading_dataframes - INFO - Created new trade entry for SENSEX 18 SEP 81500 CALL at price 175.4
2025-09-14 12:32:39,706 - api - INFO - Looking for instrument with trading symbol: SENSEX18SEP20258100000CE
2025-09-14 12:32:39,712 - trade - INFO - Dataframe updated: Trade entry created for SENSEX 18 SEP 81500 CALL
2025-09-14 12:32:39,737 - api - INFO - Looking for instrument with trading symbol: SENSEX18SEP20258150000CE
2025-09-14 12:32:39,758 - market_data - WARNING - No historical price data available for NIFTY after trying multiple dates
2025-09-14 12:32:39,787 - api - INFO - Executing trade: BUY 75 units (1 lots x 75) of NIFTY16SEP2525100CE
2025-09-14 12:32:39,791 - api - INFO - Executing trade: BUY 75 units (1 lots x 75) of NIFTY16SEP2525000CE
2025-09-14 12:32:39,783 - market_data - WARNING - No historical price data available for NIFTY after trying multiple dates
2025-09-14 12:32:39,807 - market_data - WARNING - No historical price available for NIFTY
2025-09-14 12:32:39,835 - market_data - WARNING - No historical price data available for NIFTY after trying multiple dates
2025-09-14 12:32:39,837 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:32:39,837 - market_data - WARNING - No historical price available for NIFTY
2025-09-14 12:32:39,837 - market_data - WARNING - No historical price available for NIFTY
2025-09-14 12:32:39,837 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:32:39,837 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:32:39,837 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:32:39,838 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 24900 PUT at price 85.3
2025-09-14 12:32:39,838 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:32:39,839 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:32:39,839 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 24900 PUT
2025-09-14 12:32:39,839 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25200 CALL at price 145.2
2025-09-14 12:32:39,840 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25300 CALL at price 125.7
2025-09-14 12:32:39,840 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252490000PE
2025-09-14 12:32:39,840 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25200 CALL
2025-09-14 12:32:39,840 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25300 CALL
2025-09-14 12:32:39,855 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252520000CE
2025-09-14 12:32:39,856 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252530000CE
2025-09-14 12:32:39,971 - api - INFO - Executing trade: BUY 20 units (1 lots x 20) of SENSEX2591882500CE
2025-09-14 12:32:39,979 - api - INFO - Executing trade: BUY 20 units (1 lots x 20) of SENSEX2591882000CE
2025-09-14 12:32:40,068 - api - INFO - Executing trade: BUY 20 units (1 lots x 20) of SENSEX2591883000CE
2025-09-14 12:32:40,077 - api - INFO - Executing trade: BUY 20 units (1 lots x 20) of SENSEX2591881500PE
2025-09-14 12:32:40,082 - api - INFO - Executing trade: BUY 20 units (1 lots x 20) of SENSEX2591881000CE
2025-09-14 12:32:40,128 - api - INFO - Executing trade: BUY 75 units (1 lots x 75) of NIFTY16SEP2524900CE
2025-09-14 12:32:40,145 - api - INFO - Order placed successfully with ID: 091422fd5dc5AO
2025-09-14 12:32:40,145 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '091422fd5dc5AO', 'price': 91.85, 'quantity': 75, 'lots': 1}
2025-09-14 12:32:40,145 - MultiInstrumentTest - INFO - Worker 1 (NIFTY): ✅ UNKNOWN (2.937s)
2025-09-14 12:32:40,170 - api - INFO - Order placed successfully with ID: 0914e96463eeAO
2025-09-14 12:32:40,170 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '0914e96463eeAO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:32:40,171 - MultiInstrumentTest - INFO - Worker 0 (NIFTY): ✅ UNKNOWN (2.963s)
2025-09-14 12:32:40,181 - api - INFO - Executing trade: BUY 75 units (1 lots x 75) of NIFTY16SEP2525300PE
2025-09-14 12:32:40,198 - api - INFO - Executing trade: BUY 75 units (1 lots x 75) of NIFTY16SEP2525200PE
2025-09-14 12:32:40,222 - api - INFO - Order placed successfully with ID: 0914a30b8b88AO
2025-09-14 12:32:40,222 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 20 units', 'order_id': '0914a30b8b88AO', 'price': 346.5, 'quantity': 20, 'lots': 1}
2025-09-14 12:32:40,222 - MultiInstrumentTest - INFO - Worker 5 (SENSEX): ✅ UNKNOWN (3.013s)
2025-09-14 12:32:40,280 - api - INFO - Order placed successfully with ID: 0914e696e2b4AO
2025-09-14 12:32:40,280 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 20 units', 'order_id': '0914e696e2b4AO', 'price': 145.2, 'quantity': 20, 'lots': 1}
2025-09-14 12:32:40,280 - MultiInstrumentTest - INFO - Worker 7 (SENSEX): ✅ UNKNOWN (3.071s)
2025-09-14 12:32:40,338 - api - INFO - Order placed successfully with ID: 091458f7967bAO
2025-09-14 12:32:40,338 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 20 units', 'order_id': '091458f7967bAO', 'price': 158.45, 'quantity': 20, 'lots': 1}
2025-09-14 12:32:40,338 - MultiInstrumentTest - INFO - Worker 6 (SENSEX): ✅ UNKNOWN (3.129s)
2025-09-14 12:32:40,354 - api - INFO - Order placed successfully with ID: 0914cfc48428AO
2025-09-14 12:32:40,354 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 20 units', 'order_id': '0914cfc48428AO', 'price': 53.5, 'quantity': 20, 'lots': 1}
2025-09-14 12:32:40,355 - MultiInstrumentTest - INFO - Worker 9 (SENSEX): ✅ UNKNOWN (3.144s)
2025-09-14 12:32:40,358 - api - INFO - Order placed successfully with ID: 0914b6e062e8AO
2025-09-14 12:32:40,358 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 20 units', 'order_id': '0914b6e062e8AO', 'price': 1090.85, 'quantity': 20, 'lots': 1}
2025-09-14 12:32:40,358 - MultiInstrumentTest - INFO - Worker 8 (SENSEX): ✅ UNKNOWN (3.149s)
2025-09-14 12:32:40,381 - api - INFO - Order placed successfully with ID: 0914d079fc77AO
2025-09-14 12:32:40,383 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '0914d079fc77AO', 'price': 183.55, 'quantity': 75, 'lots': 1}
2025-09-14 12:32:40,382 - api - INFO - Order placed successfully with ID: 0914dabb98cbAO
2025-09-14 12:32:40,385 - api - INFO - Order placed successfully with ID: 09148aec66d7AO
2025-09-14 12:32:40,385 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '0914dabb98cbAO', 'price': 250.7, 'quantity': 75, 'lots': 1}
2025-09-14 12:32:40,385 - MultiInstrumentTest - INFO - Worker 4 (NIFTY): ✅ UNKNOWN (3.176s)
2025-09-14 12:32:40,385 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '09148aec66d7AO', 'price': 109.95, 'quantity': 75, 'lots': 1}
2025-09-14 12:32:40,385 - MultiInstrumentTest - INFO - Worker 3 (NIFTY): ✅ UNKNOWN (3.177s)
2025-09-14 12:32:40,386 - MultiInstrumentTest - INFO - Worker 2 (NIFTY): ✅ UNKNOWN (3.178s)
