2025-09-14 12:28:16,968 - api - INFO - SmartAPI client initialized successfully
2025-09-14 12:28:17,141 - api - INFO - Loaded 127276 instruments from Dependencies/all_instrument 2025-09-14.csv
2025-09-14 12:29:55,092 - api - INFO - SmartAPI client initialized successfully
2025-09-14 12:29:55,279 - api - INFO - Loaded 127276 instruments from Dependencies/all_instrument 2025-09-14.csv
2025-09-14 12:29:55,348 - market_data - INFO - Market Data Manager initialized
2025-09-14 12:29:55,349 - instrument_updater - INFO - Instrument Updater initialized
2025-09-14 12:29:55,349 - instrument_updater - INFO - Today's instrument file already exists
2025-09-14 12:29:55,349 - instrument_updater - INFO - Instrument update not needed
2025-09-14 12:29:55,349 - trading_dataframes - INFO - Instrument files updated successfully
2025-09-14 12:29:55,509 - api - INFO - Loaded 127276 instruments from Dependencies/all_instrument 2025-09-14.csv
2025-09-14 12:29:55,514 - trading_dataframes - INFO - Reloaded instruments in API handler
2025-09-14 12:29:55,514 - trading_dataframes - INFO - Trading Dataframe Manager initialized with market data integration
2025-09-14 12:29:55,515 - ConcurrentTest - INFO - 🎯 Starting comprehensive concurrent execution tests...
2025-09-14 12:29:55,515 - ConcurrentTest - INFO - 🚀 Testing 10 concurrent BUY signals...
2025-09-14 12:29:55,540 - ConcurrentTest - INFO - Worker 9: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:29:55,567 - ConcurrentTest - INFO - Worker 6: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:29:55,595 - ConcurrentTest - INFO - Worker 7: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:29:55,647 - ConcurrentTest - INFO - Worker 5: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:29:55,731 - ConcurrentTest - INFO - Worker 0: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:29:55,844 - ConcurrentTest - INFO - Worker 8: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:29:55,893 - ConcurrentTest - INFO - Worker 1: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:29:55,937 - ConcurrentTest - INFO - Worker 3: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:29:55,981 - ConcurrentTest - INFO - Worker 4: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:29:55,991 - ConcurrentTest - INFO - Worker 2: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:29:57,997 - ConcurrentTest - INFO - 💥 Testing burst of 15 mixed signals...
2025-09-14 12:29:57,999 - ConcurrentTest - INFO - Worker 0: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:29:58,001 - ConcurrentTest - INFO - Worker 1: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:29:58,001 - ConcurrentTest - INFO - Worker 2: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:29:58,002 - ConcurrentTest - INFO - Worker 3: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:29:58,002 - ConcurrentTest - INFO - Worker 4: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:29:58,003 - ConcurrentTest - INFO - Worker 5: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:29:58,004 - ConcurrentTest - INFO - Worker 6: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:29:58,005 - ConcurrentTest - INFO - Worker 7: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:29:58,006 - ConcurrentTest - INFO - Worker 8: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:29:58,006 - ConcurrentTest - INFO - Worker 9: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:29:58,006 - ConcurrentTest - INFO - Worker 10: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:29:58,007 - ConcurrentTest - INFO - Worker 11: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:29:58,007 - ConcurrentTest - INFO - Worker 12: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:29:58,007 - ConcurrentTest - INFO - Worker 13: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:29:58,007 - ConcurrentTest - INFO - Worker 14: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:30:00,013 - ConcurrentTest - INFO - 🔒 Testing database lock resilience with 20 threads...
2025-09-14 12:30:00,014 - ConcurrentTest - INFO - Worker 0: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:30:00,014 - ConcurrentTest - INFO - Worker 1: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:30:00,015 - ConcurrentTest - INFO - Worker 2: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:30:00,016 - ConcurrentTest - INFO - Worker 3: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:30:00,017 - ConcurrentTest - INFO - Worker 4: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:30:00,018 - ConcurrentTest - INFO - Worker 5: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:30:00,018 - ConcurrentTest - INFO - Worker 6: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:30:00,019 - ConcurrentTest - INFO - Worker 7: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:30:00,020 - ConcurrentTest - INFO - Worker 8: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:30:00,021 - ConcurrentTest - INFO - Worker 9: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:30:00,021 - ConcurrentTest - INFO - Worker 10: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:30:00,022 - ConcurrentTest - INFO - Worker 11: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:30:00,022 - ConcurrentTest - INFO - Worker 12: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:30:00,022 - ConcurrentTest - INFO - Worker 13: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:30:00,022 - ConcurrentTest - INFO - Worker 14: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:30:00,022 - ConcurrentTest - INFO - Worker 15: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:30:00,023 - ConcurrentTest - INFO - Worker 16: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:30:00,023 - ConcurrentTest - INFO - Worker 17: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:30:00,023 - ConcurrentTest - INFO - Worker 18: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:30:00,023 - ConcurrentTest - INFO - Worker 19: ❌ UNKNOWN UNKNOWN (0.000s)
2025-09-14 12:30:48,739 - api - INFO - SmartAPI client initialized successfully
2025-09-14 12:30:48,918 - api - INFO - Loaded 127276 instruments from Dependencies/all_instrument 2025-09-14.csv
2025-09-14 12:30:48,986 - market_data - INFO - Market Data Manager initialized
2025-09-14 12:30:48,987 - instrument_updater - INFO - Instrument Updater initialized
2025-09-14 12:30:48,988 - instrument_updater - INFO - Today's instrument file already exists
2025-09-14 12:30:48,988 - instrument_updater - INFO - Instrument update not needed
2025-09-14 12:30:48,988 - trading_dataframes - INFO - Instrument files updated successfully
2025-09-14 12:30:49,141 - api - INFO - Loaded 127276 instruments from Dependencies/all_instrument 2025-09-14.csv
2025-09-14 12:30:49,147 - trading_dataframes - INFO - Reloaded instruments in API handler
2025-09-14 12:30:49,147 - trading_dataframes - INFO - Trading Dataframe Manager initialized with market data integration
2025-09-14 12:30:49,147 - ConcurrentTest - INFO - 🎯 Starting comprehensive concurrent execution tests...
2025-09-14 12:30:49,147 - ConcurrentTest - INFO - 🚀 Testing 10 concurrent BUY signals...
2025-09-14 12:30:49,214 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$SENSEX 18 SEP 82000 PUT$171.24$162.68$2025-09-12 13:00:32+05:30
2025-09-14 12:30:49,214 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$SENSEX 18 SEP 81500 PUT$140.84$133.8$2025-09-12 10:11:07+05:30
2025-09-14 12:30:49,215 - trade - INFO - Parsed signal: TradeSignal(symbol='SENSEX', action='BUY', strike=82000.0, option_type='PE', timestamp=datetime.datetime(2025, 9, 12, 13, 0, 32, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=171.24, stop_loss=162.68, entry_price=171.24, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='18 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:49,215 - trade - INFO - Parsed signal: TradeSignal(symbol='SENSEX', action='BUY', strike=81500.0, option_type='PE', timestamp=datetime.datetime(2025, 9, 12, 10, 11, 7, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=140.84, stop_loss=133.8, entry_price=140.84, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='18 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:49,216 - trading_dataframes - INFO - Processing signal: TRADE - BUY for SENSEX
2025-09-14 12:30:49,216 - trading_dataframes - INFO - Processing signal: TRADE - BUY for SENSEX
2025-09-14 12:30:49,217 - api - INFO - Looking for instrument with trading symbol: SENSEX18SEP20258200000PE
2025-09-14 12:30:49,218 - api - INFO - Looking for instrument with trading symbol: SENSEX18SEP20258150000PE
2025-09-14 12:30:49,254 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25200 CALL$170.82$162.28$2025-09-12 10:24:50+05:30
2025-09-14 12:30:49,254 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25200.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 10, 24, 50, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=170.82, stop_loss=162.28, entry_price=170.82, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:49,255 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:30:49,255 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252520000CE
2025-09-14 12:30:49,322 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$SENSEX 18 SEP 81500 PUT$172.22$163.61$2025-09-12 13:35:15+05:30
2025-09-14 12:30:49,308 - market_data - INFO - Market is closed, fetching historical price for SENSEX
2025-09-14 12:30:49,332 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25200 PUT$77.89$74.0$2025-09-12 12:32:44+05:30
2025-09-14 12:30:49,339 - trade - INFO - Parsed signal: TradeSignal(symbol='SENSEX', action='BUY', strike=81500.0, option_type='PE', timestamp=datetime.datetime(2025, 9, 12, 13, 35, 15, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=172.22, stop_loss=163.61, entry_price=172.22, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='18 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:49,332 - market_data - INFO - Market is closed, fetching historical price for SENSEX
2025-09-14 12:30:49,342 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-12
2025-09-14 12:30:49,343 - market_data - INFO - Market is closed, fetching historical price for NIFTY
2025-09-14 12:30:49,343 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25200.0, option_type='PE', timestamp=datetime.datetime(2025, 9, 12, 12, 32, 44, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=77.89, stop_loss=74.0, entry_price=77.89, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:49,343 - trading_dataframes - INFO - Processing signal: TRADE - BUY for SENSEX
2025-09-14 12:30:49,343 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-12
2025-09-14 12:30:49,344 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-12
2025-09-14 12:30:49,344 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:30:49,345 - api - INFO - Looking for instrument with trading symbol: SENSEX18SEP20258150000PE
2025-09-14 12:30:49,346 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252520000PE
2025-09-14 12:30:49,419 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25200 CALL$94.28$89.57$2025-09-12 11:34:04+05:30
2025-09-14 12:30:49,422 - market_data - INFO - Market is closed, fetching historical price for NIFTY
2025-09-14 12:30:49,423 - market_data - INFO - Market is closed, fetching historical price for SENSEX
2025-09-14 12:30:49,423 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-12
2025-09-14 12:30:49,423 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25200.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 11, 34, 4, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=94.28, stop_loss=89.57, entry_price=94.28, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:49,423 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-12
2025-09-14 12:30:49,424 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:30:49,424 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252520000CE
2025-09-14 12:30:49,438 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$SENSEX 18 SEP 81000 PUT$161.0$152.95$2025-09-12 12:29:14+05:30
2025-09-14 12:30:49,442 - trade - INFO - Parsed signal: TradeSignal(symbol='SENSEX', action='BUY', strike=81000.0, option_type='PE', timestamp=datetime.datetime(2025, 9, 12, 12, 29, 14, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=161.0, stop_loss=152.95, entry_price=161.0, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='18 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:49,469 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 PUT$69.19$65.73$2025-09-12 13:16:17+05:30
2025-09-14 12:30:49,469 - market_data - INFO - Market is closed, fetching historical price for NIFTY
2025-09-14 12:30:49,469 - trading_dataframes - INFO - Processing signal: TRADE - BUY for SENSEX
2025-09-14 12:30:49,469 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='PE', timestamp=datetime.datetime(2025, 9, 12, 13, 16, 17, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=69.19, stop_loss=65.73, entry_price=69.19, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:49,469 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-12
2025-09-14 12:30:49,470 - api - INFO - Looking for instrument with trading symbol: SENSEX18SEP20258100000PE
2025-09-14 12:30:49,470 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:30:49,486 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000PE
2025-09-14 12:30:49,533 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25200 PUT$66.5$63.17$2025-09-12 12:45:46+05:30
2025-09-14 12:30:49,533 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25200.0, option_type='PE', timestamp=datetime.datetime(2025, 9, 12, 12, 45, 46, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=66.5, stop_loss=63.17, entry_price=66.5, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:49,533 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:30:49,539 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$SENSEX 18 SEP 82000 PUT$51.28$48.72$2025-09-12 14:40:34+05:30
2025-09-14 12:30:49,543 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252520000PE
2025-09-14 12:30:49,568 - market_data - INFO - Market is closed, fetching historical price for SENSEX
2025-09-14 12:30:49,567 - trade - INFO - Parsed signal: TradeSignal(symbol='SENSEX', action='BUY', strike=82000.0, option_type='PE', timestamp=datetime.datetime(2025, 9, 12, 14, 40, 34, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=51.28, stop_loss=48.72, entry_price=51.28, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='18 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:49,568 - market_data - INFO - Market is closed, fetching historical price for NIFTY
2025-09-14 12:30:49,583 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-12
2025-09-14 12:30:49,598 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-11
2025-09-14 12:30:49,609 - market_data - INFO - Market is closed, fetching historical price for NIFTY
2025-09-14 12:30:49,609 - trading_dataframes - INFO - Processing signal: TRADE - BUY for SENSEX
2025-09-14 12:30:49,609 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-12
2025-09-14 12:30:49,610 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-12
2025-09-14 12:30:49,611 - api - INFO - Looking for instrument with trading symbol: SENSEX18SEP20258200000PE
2025-09-14 12:30:49,653 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-11
2025-09-14 12:30:49,662 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-11
2025-09-14 12:30:49,667 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-11
2025-09-14 12:30:49,683 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-11
2025-09-14 12:30:49,697 - market_data - INFO - Market is closed, fetching historical price for SENSEX
2025-09-14 12:30:49,698 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-12
2025-09-14 12:30:49,788 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-11
2025-09-14 12:30:50,091 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-11
2025-09-14 12:30:50,094 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-10
2025-09-14 12:30:50,151 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-11
2025-09-14 12:30:50,161 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-10
2025-09-14 12:30:50,163 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-10
2025-09-14 12:30:50,166 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-11
2025-09-14 12:30:50,169 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-10
2025-09-14 12:30:50,176 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-10
2025-09-14 12:30:50,182 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-10
2025-09-14 12:30:50,243 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-09
2025-09-14 12:30:50,288 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-10
2025-09-14 12:30:50,603 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-10
2025-09-14 12:30:50,619 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-10
2025-09-14 12:30:50,628 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-09
2025-09-14 12:30:50,642 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-09
2025-09-14 12:30:50,644 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-09
2025-09-14 12:30:50,651 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-08
2025-09-14 12:30:50,655 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-09
2025-09-14 12:30:50,661 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-09
2025-09-14 12:30:50,670 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-09
2025-09-14 12:30:50,811 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-09
2025-09-14 12:30:50,855 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-09
2025-09-14 12:30:50,913 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-08
2025-09-14 12:30:51,076 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-08
2025-09-14 12:30:51,112 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-08
2025-09-14 12:30:51,114 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-08
2025-09-14 12:30:51,120 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-08
2025-09-14 12:30:51,144 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-08
2025-09-14 12:30:51,147 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-08
2025-09-14 12:30:51,148 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-08
2025-09-14 12:30:51,170 - market_data - WARNING - No historical price data available for SENSEX after trying multiple dates
2025-09-14 12:30:51,170 - market_data - WARNING - No historical price available for SENSEX
2025-09-14 12:30:51,170 - trading_dataframes - WARNING - Could not retrieve market price for SENSEX
2025-09-14 12:30:51,170 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:30:51,174 - market_data - WARNING - No historical price data available for SENSEX after trying multiple dates
2025-09-14 12:30:51,174 - market_data - WARNING - No historical price available for SENSEX
2025-09-14 12:30:51,174 - trading_dataframes - WARNING - Could not retrieve market price for SENSEX
2025-09-14 12:30:51,176 - trading_dataframes - INFO - Created new trade entry for SENSEX 18 SEP 81500 PUT at price 172.22
2025-09-14 12:30:51,176 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:30:51,176 - trade - INFO - Dataframe updated: Trade entry created for SENSEX 18 SEP 81500 PUT
2025-09-14 12:30:51,177 - trading_dataframes - INFO - Created new trade entry for SENSEX 18 SEP 82000 PUT at price 171.24
2025-09-14 12:30:51,179 - trade - INFO - Dataframe updated: Trade entry created for SENSEX 18 SEP 82000 PUT
2025-09-14 12:30:51,178 - api - INFO - Looking for instrument with trading symbol: SENSEX18SEP20258150000PE
2025-09-14 12:30:51,179 - api - INFO - Looking for instrument with trading symbol: SENSEX18SEP20258200000PE
2025-09-14 12:30:51,265 - market_data - WARNING - No historical price data available for SENSEX after trying multiple dates
2025-09-14 12:30:51,271 - market_data - WARNING - No historical price available for SENSEX
2025-09-14 12:30:51,284 - trading_dataframes - WARNING - Could not retrieve market price for SENSEX
2025-09-14 12:30:51,285 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:30:51,286 - trading_dataframes - INFO - Created new trade entry for SENSEX 18 SEP 81500 PUT at price 140.84
2025-09-14 12:30:51,286 - trade - INFO - Dataframe updated: Trade entry created for SENSEX 18 SEP 81500 PUT
2025-09-14 12:30:51,286 - api - INFO - Looking for instrument with trading symbol: SENSEX18SEP20258150000PE
2025-09-14 12:30:51,360 - market_data - WARNING - No historical price data available for NIFTY after trying multiple dates
2025-09-14 12:30:51,360 - market_data - WARNING - No historical price available for NIFTY
2025-09-14 12:30:51,360 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:30:51,360 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:30:51,361 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25200 CALL at price 170.82
2025-09-14 12:30:51,361 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25200 CALL
2025-09-14 12:30:51,361 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252520000CE
2025-09-14 12:30:51,375 - market_data - WARNING - No historical price data available for NIFTY after trying multiple dates
2025-09-14 12:30:51,376 - market_data - WARNING - No historical price data available for NIFTY after trying multiple dates
2025-09-14 12:30:51,378 - market_data - WARNING - No historical price data available for SENSEX after trying multiple dates
2025-09-14 12:30:51,389 - market_data - WARNING - No historical price data available for NIFTY after trying multiple dates
2025-09-14 12:30:51,389 - market_data - WARNING - No historical price available for NIFTY
2025-09-14 12:30:51,397 - market_data - WARNING - No historical price data available for SENSEX after trying multiple dates
2025-09-14 12:30:51,408 - market_data - WARNING - No historical price available for NIFTY
2025-09-14 12:30:51,409 - market_data - WARNING - No historical price available for SENSEX
2025-09-14 12:30:51,410 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:30:51,410 - market_data - WARNING - No historical price available for NIFTY
2025-09-14 12:30:51,410 - market_data - WARNING - No historical price available for SENSEX
2025-09-14 12:30:51,410 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:30:51,410 - trading_dataframes - WARNING - Could not retrieve market price for SENSEX
2025-09-14 12:30:51,411 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:30:51,411 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:30:51,411 - trading_dataframes - WARNING - Could not retrieve market price for SENSEX
2025-09-14 12:30:51,411 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:30:51,411 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:30:51,412 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25200 PUT at price 77.89
2025-09-14 12:30:51,412 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:30:51,412 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:30:51,412 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 PUT at price 69.19
2025-09-14 12:30:51,413 - trading_dataframes - INFO - Created new trade entry for SENSEX 18 SEP 82000 PUT at price 51.28
2025-09-14 12:30:51,414 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25200 PUT at price 66.5
2025-09-14 12:30:51,413 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25200 PUT
2025-09-14 12:30:51,414 - trading_dataframes - INFO - Created new trade entry for SENSEX 18 SEP 81000 PUT at price 161.0
2025-09-14 12:30:51,415 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 PUT
2025-09-14 12:30:51,415 - trade - INFO - Dataframe updated: Trade entry created for SENSEX 18 SEP 82000 PUT
2025-09-14 12:30:51,415 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25200 PUT
2025-09-14 12:30:51,415 - trade - INFO - Dataframe updated: Trade entry created for SENSEX 18 SEP 81000 PUT
2025-09-14 12:30:51,415 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252520000PE
2025-09-14 12:30:51,416 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000PE
2025-09-14 12:30:51,416 - api - INFO - Looking for instrument with trading symbol: SENSEX18SEP20258200000PE
2025-09-14 12:30:51,416 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252520000PE
2025-09-14 12:30:51,416 - api - INFO - Looking for instrument with trading symbol: SENSEX18SEP20258100000PE
2025-09-14 12:30:51,519 - api - INFO - Executing trade: BUY 20 units (1 lots x 20) of SENSEX2591881500PE
2025-09-14 12:30:51,526 - api - INFO - Executing trade: BUY 20 units (1 lots x 20) of SENSEX2591882000CE
2025-09-14 12:30:51,650 - api - INFO - Executing trade: BUY 75 units (1 lots x 75) of NIFTY16SEP2525200PE
2025-09-14 12:30:51,725 - api - INFO - Executing trade: BUY 20 units (1 lots x 20) of SENSEX2591881500PE
2025-09-14 12:30:51,747 - api - INFO - Executing trade: BUY 75 units (1 lots x 75) of NIFTY16SEP2525000CE
2025-09-14 12:30:51,870 - api - INFO - Executing trade: BUY 75 units (1 lots x 75) of NIFTY16SEP2525200PE
2025-09-14 12:30:52,001 - api - INFO - Executing trade: BUY 75 units (1 lots x 75) of NIFTY16SEP2525200PE
2025-09-14 12:30:52,003 - api - INFO - Order placed successfully with ID: 09140d695013AO
2025-09-14 12:30:52,003 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 20 units', 'order_id': '09140d695013AO', 'price': 346.5, 'quantity': 20, 'lots': 1}
2025-09-14 12:30:52,004 - ConcurrentTest - INFO - Worker 5: ✅ UNKNOWN UNKNOWN (2.790s)
2025-09-14 12:30:52,017 - api - INFO - Order placed successfully with ID: 09147716a69eAO
2025-09-14 12:30:52,019 - api - INFO - Executing trade: BUY 20 units (1 lots x 20) of SENSEX2591881000CE
2025-09-14 12:30:52,019 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 20 units', 'order_id': '09147716a69eAO', 'price': 158.45, 'quantity': 20, 'lots': 1}
2025-09-14 12:30:52,020 - ConcurrentTest - INFO - Worker 7: ✅ UNKNOWN UNKNOWN (2.713s)
2025-09-14 12:30:52,046 - api - INFO - Order placed successfully with ID: 0914c2278062AO
2025-09-14 12:30:52,046 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '0914c2278062AO', 'price': 109.95, 'quantity': 75, 'lots': 1}
2025-09-14 12:30:52,046 - ConcurrentTest - INFO - Worker 6: ✅ UNKNOWN UNKNOWN (2.799s)
2025-09-14 12:30:52,079 - api - INFO - Executing trade: BUY 20 units (1 lots x 20) of SENSEX2591882000CE
2025-09-14 12:30:52,096 - api - INFO - Order placed successfully with ID: 0914880880ecAO
2025-09-14 12:30:52,096 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '0914880880ecAO', 'price': 109.95, 'quantity': 75, 'lots': 1}
2025-09-14 12:30:52,096 - ConcurrentTest - INFO - Worker 4: ✅ UNKNOWN UNKNOWN (2.808s)
2025-09-14 12:30:52,107 - api - INFO - Order placed successfully with ID: 09148aa812a2AO
2025-09-14 12:30:52,107 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '09148aa812a2AO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:30:52,108 - ConcurrentTest - INFO - Worker 2: ✅ UNKNOWN UNKNOWN (2.641s)
2025-09-14 12:30:52,114 - api - INFO - Order placed successfully with ID: 091490fccadbAO
2025-09-14 12:30:52,114 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 20 units', 'order_id': '091490fccadbAO', 'price': 158.45, 'quantity': 20, 'lots': 1}
2025-09-14 12:30:52,114 - ConcurrentTest - INFO - Worker 3: ✅ UNKNOWN UNKNOWN (2.900s)
2025-09-14 12:30:52,211 - api - INFO - Order placed successfully with ID: 091452913c9dAO
2025-09-14 12:30:52,211 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '091452913c9dAO', 'price': 109.95, 'quantity': 75, 'lots': 1}
2025-09-14 12:30:52,211 - ConcurrentTest - INFO - Worker 0: ✅ UNKNOWN UNKNOWN (2.695s)
2025-09-14 12:30:52,276 - api - INFO - Order placed successfully with ID: 0914eada5ef4AO
2025-09-14 12:30:52,276 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 20 units', 'order_id': '0914eada5ef4AO', 'price': 346.5, 'quantity': 20, 'lots': 1}
2025-09-14 12:30:52,277 - ConcurrentTest - INFO - Worker 9: ✅ UNKNOWN UNKNOWN (2.738s)
2025-09-14 12:30:52,297 - api - INFO - Order placed successfully with ID: 0914ca4c8261AO
2025-09-14 12:30:52,297 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 20 units', 'order_id': '0914ca4c8261AO', 'price': 1090.85, 'quantity': 20, 'lots': 1}
2025-09-14 12:30:52,298 - ConcurrentTest - INFO - Worker 1: ✅ UNKNOWN UNKNOWN (2.861s)
2025-09-14 12:30:52,736 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-11
2025-09-14 12:30:52,903 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-10
2025-09-14 12:30:53,063 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-09
2025-09-14 12:30:53,206 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-08
2025-09-14 12:30:53,358 - market_data - WARNING - No historical price data available for NIFTY after trying multiple dates
2025-09-14 12:30:53,358 - market_data - WARNING - No historical price available for NIFTY
2025-09-14 12:30:53,358 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:30:53,358 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:30:53,359 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25200 CALL at price 94.28
2025-09-14 12:30:53,359 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25200 CALL
2025-09-14 12:30:53,360 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252520000CE
2025-09-14 12:30:53,613 - api - INFO - Executing trade: BUY 75 units (1 lots x 75) of NIFTY16SEP2525200PE
2025-09-14 12:30:53,819 - api - INFO - Order placed successfully with ID: 0914dd17cc0eAO
2025-09-14 12:30:53,819 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '0914dd17cc0eAO', 'price': 109.95, 'quantity': 75, 'lots': 1}
2025-09-14 12:30:53,819 - ConcurrentTest - INFO - Worker 8: ✅ UNKNOWN UNKNOWN (4.408s)
2025-09-14 12:30:55,825 - ConcurrentTest - INFO - 💥 Testing burst of 15 mixed signals...
2025-09-14 12:30:55,829 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$CLOSE$SENSEX 18 SEP 81000 CALL$136.83$2025-09-12 15:37:13+05:30$profit_booking$136.83
2025-09-14 12:30:55,829 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$CLOSE$NIFTY 16 SEP 24900 PUT$157.91$2025-09-12 14:07:15+05:30$profit_booking$157.91
2025-09-14 12:30:55,829 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$CLOSE$NIFTY 16 SEP 25100 CALL$52.4$2025-09-12 09:16:42+05:30$profit_booking$52.4
2025-09-14 12:30:55,829 - trade - INFO - Parsed signal: TradeSignal(symbol='SENSEX', action='CLOSE', strike=81000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 15, 37, 13, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=136.83, stop_loss=None, entry_price=None, exit_price=136.83, current_price=None, exit_reason='profit_booking', signal_type='TRADE', expiry_date='18 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:55,831 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25200 CALL$83.04$78.89$2025-09-12 10:52:25+05:30
2025-09-14 12:30:55,831 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$SENSEX 18 SEP 83000 CALL$136.67$129.84$2025-09-12 09:17:06+05:30
2025-09-14 12:30:55,833 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='CLOSE', strike=24900.0, option_type='PE', timestamp=datetime.datetime(2025, 9, 12, 14, 7, 15, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=157.91, stop_loss=None, entry_price=None, exit_price=157.91, current_price=None, exit_reason='profit_booking', signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:55,833 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$CLOSE$SENSEX 18 SEP 81500 CALL$151.95$2025-09-12 15:14:10+05:30$profit_booking$151.95
2025-09-14 12:30:55,836 - trading_dataframes - INFO - Processing signal: TRADE - CLOSE for SENSEX
2025-09-14 12:30:55,834 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 24900 CALL$102.62$97.49$2025-09-12 15:00:03+05:30
2025-09-14 12:30:55,839 - trading_dataframes - INFO - Processing signal: TRADE - CLOSE for NIFTY
2025-09-14 12:30:55,834 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$CLOSE$NIFTY 16 SEP 24800 CALL$117.38$2025-09-12 13:33:53+05:30$profit_booking$117.38
2025-09-14 12:30:55,834 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='CLOSE', strike=25100.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 9, 16, 42, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=52.4, stop_loss=None, entry_price=None, exit_price=52.4, current_price=None, exit_reason='profit_booking', signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:55,834 - trade - INFO - Processing message: ===Algo_Trading===$INTIMATION$Continue to hold trade for $SENSEX 16 SEP 83000 CALL at current price: 164.47
2025-09-14 12:30:55,840 - trading_dataframes - ERROR - No active trade found for SENSEX 18 SEP 81000 CALL to close
2025-09-14 12:30:55,835 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$CLOSE$SENSEX 18 SEP 82500 PUT$150.59$2025-09-12 11:42:26+05:30$profit_booking$150.59
2025-09-14 12:30:55,835 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$CLOSE$SENSEX 18 SEP 81500 CALL$119.59$2025-09-12 10:44:34+05:30$profit_booking$119.59
2025-09-14 12:30:55,836 - trade - INFO - Processing message: ===Algo_Trading===$Update$STOP LOSS to$68.94$for option$SENSEX 16 SEP 83000 CALL
2025-09-14 12:30:55,837 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$CLOSE$SENSEX 18 SEP 83000 PUT$186.5$2025-09-12 10:28:14+05:30$profit_booking$186.5
2025-09-14 12:30:55,837 - trade - INFO - Processing message: ===Algo_Trading===$INTIMATION$Continue to hold trade for $SENSEX 16 SEP 82000 CALL at current price: 106.42
2025-09-14 12:30:55,837 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 PUT$114.95$109.2$2025-09-12 12:50:58+05:30
2025-09-14 12:30:55,838 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25200.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 10, 52, 25, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=83.04, stop_loss=78.89, entry_price=83.04, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:55,838 - trade - INFO - Parsed signal: TradeSignal(symbol='SENSEX', action='BUY', strike=83000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 9, 17, 6, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=136.67, stop_loss=129.84, entry_price=136.67, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='18 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:55,841 - trading_dataframes - ERROR - No active trade found for NIFTY 16 SEP 24900 PUT to close
2025-09-14 12:30:55,839 - trade - INFO - Parsed signal: TradeSignal(symbol='SENSEX', action='CLOSE', strike=81500.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 15, 14, 10, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=151.95, stop_loss=None, entry_price=None, exit_price=151.95, current_price=None, exit_reason='profit_booking', signal_type='TRADE', expiry_date='18 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:55,841 - trading_dataframes - INFO - Processing signal: TRADE - CLOSE for NIFTY
2025-09-14 12:30:55,840 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=24900.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 15, 0, 3, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=102.62, stop_loss=97.49, entry_price=102.62, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:55,841 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='CLOSE', strike=24800.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 13, 33, 53, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=117.38, stop_loss=None, entry_price=None, exit_price=117.38, current_price=None, exit_reason='profit_booking', signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:55,843 - trade - INFO - Parsed signal: TradeSignal(symbol='SENSEX', action='HOLD', strike=83000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 14, 12, 30, 48, 353070), quantity=1, price=164.47, stop_loss=None, entry_price=None, exit_price=None, current_price=164.47, exit_reason=None, signal_type='INTIMATION', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:55,843 - trade - ERROR - Dataframe update failed: No active trade found for SENSEX 18 SEP 81000 CALL to close
2025-09-14 12:30:55,844 - trade - INFO - Parsed signal: TradeSignal(symbol='SENSEX', action='CLOSE', strike=82500.0, option_type='PE', timestamp=datetime.datetime(2025, 9, 12, 11, 42, 26, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=150.59, stop_loss=None, entry_price=None, exit_price=150.59, current_price=None, exit_reason='profit_booking', signal_type='TRADE', expiry_date='18 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:55,845 - trade - INFO - Parsed signal: TradeSignal(symbol='SENSEX', action='CLOSE', strike=81500.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 10, 44, 34, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=119.59, stop_loss=None, entry_price=None, exit_price=119.59, current_price=None, exit_reason='profit_booking', signal_type='TRADE', expiry_date='18 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:55,845 - trade - INFO - Parsed signal: TradeSignal(symbol='SENSEX', action='UPDATE', strike=83000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 14, 12, 30, 48, 353070), quantity=1, price=None, stop_loss=None, entry_price=None, exit_price=None, current_price=None, exit_reason=None, signal_type='UPDATE', expiry_date='16 SEP', update_type='STOP LOSS', new_stop_loss=68.94)
2025-09-14 12:30:55,846 - trade - INFO - Parsed signal: TradeSignal(symbol='SENSEX', action='CLOSE', strike=83000.0, option_type='PE', timestamp=datetime.datetime(2025, 9, 12, 10, 28, 14, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=186.5, stop_loss=None, entry_price=None, exit_price=186.5, current_price=None, exit_reason='profit_booking', signal_type='TRADE', expiry_date='18 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:55,846 - trade - INFO - Parsed signal: TradeSignal(symbol='SENSEX', action='HOLD', strike=82000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 14, 12, 30, 48, 353070), quantity=1, price=106.42, stop_loss=None, entry_price=None, exit_price=None, current_price=106.42, exit_reason=None, signal_type='INTIMATION', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:55,847 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='PE', timestamp=datetime.datetime(2025, 9, 12, 12, 50, 58, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=114.95, stop_loss=109.2, entry_price=114.95, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:55,847 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:30:55,847 - trading_dataframes - INFO - Processing signal: TRADE - BUY for SENSEX
2025-09-14 12:30:55,847 - trade - ERROR - Dataframe update failed: No active trade found for NIFTY 16 SEP 24900 PUT to close
2025-09-14 12:30:55,848 - trading_dataframes - INFO - Processing signal: TRADE - CLOSE for SENSEX
2025-09-14 12:30:55,849 - api - INFO - Looking for instrument with trading symbol: SENSEX18SEP20258100000CE
2025-09-14 12:30:55,848 - trading_dataframes - ERROR - No active trade found for NIFTY 16 SEP 25100 CALL to close
2025-09-14 12:30:55,848 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:30:55,848 - trading_dataframes - INFO - Processing signal: TRADE - CLOSE for NIFTY
2025-09-14 12:30:55,848 - trading_dataframes - INFO - Processing signal: INTIMATION - HOLD for SENSEX
2025-09-14 12:30:55,849 - trading_dataframes - INFO - Processing signal: TRADE - CLOSE for SENSEX
2025-09-14 12:30:55,849 - trading_dataframes - INFO - Processing signal: TRADE - CLOSE for SENSEX
2025-09-14 12:30:55,850 - trading_dataframes - INFO - Processing signal: UPDATE - UPDATE for SENSEX
2025-09-14 12:30:55,850 - trading_dataframes - INFO - Processing signal: TRADE - CLOSE for SENSEX
2025-09-14 12:30:55,850 - trading_dataframes - INFO - Processing signal: INTIMATION - HOLD for SENSEX
2025-09-14 12:30:55,851 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:30:55,851 - trading_dataframes - WARNING - Active trade already exists for NIFTY 16 SEP 25200 CALL. Overwriting with new entry.
2025-09-14 12:30:55,853 - api - INFO - Looking for instrument with trading symbol: SENSEX18SEP20258300000CE
2025-09-14 12:30:55,854 - trading_dataframes - ERROR - No active trade found for SENSEX 18 SEP 81500 CALL to close
2025-09-14 12:30:55,854 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252490000PE
2025-09-14 12:30:55,876 - trade - ERROR - Dataframe update failed: No active trade found for NIFTY 16 SEP 25100 CALL to close
2025-09-14 12:30:55,877 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252490000CE
2025-09-14 12:30:55,878 - trading_dataframes - ERROR - No active trade found for NIFTY 16 SEP 24800 CALL to close
2025-09-14 12:30:55,884 - trading_dataframes - WARNING - Received intimation for SENSEX 16 SEP 83000 CALL but no active trade found
2025-09-14 12:30:55,890 - trading_dataframes - ERROR - No active trade found for SENSEX 18 SEP 82500 PUT to close
2025-09-14 12:30:55,921 - trading_dataframes - ERROR - No active trade found for SENSEX 18 SEP 81500 CALL to close
2025-09-14 12:30:55,922 - trading_dataframes - WARNING - Received update for SENSEX 16 SEP 83000 CALL but no active trade found
2025-09-14 12:30:55,936 - trading_dataframes - ERROR - No active trade found for SENSEX 18 SEP 83000 PUT to close
2025-09-14 12:30:55,936 - trade - ERROR - Dataframe update failed: No active trade found for SENSEX 18 SEP 83000 PUT to close
2025-09-14 12:30:55,936 - api - INFO - Looking for instrument with trading symbol: SENSEX18SEP20258300000PE
2025-09-14 12:30:55,941 - trading_dataframes - WARNING - Received intimation for SENSEX 16 SEP 82000 CALL but no active trade found
2025-09-14 12:30:55,958 - trade - ERROR - Dataframe update failed: No active trade found for SENSEX 18 SEP 81500 CALL to close
2025-09-14 12:30:55,983 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252510000CE
2025-09-14 12:30:56,010 - trade - ERROR - Dataframe update failed: No active trade found for NIFTY 16 SEP 24800 CALL to close
2025-09-14 12:30:55,992 - market_data - INFO - Market is closed, fetching historical price for SENSEX
2025-09-14 12:30:56,035 - trade - ERROR - Dataframe update failed: Intimation received for SENSEX 16 SEP 83000 CALL but no active trade found
2025-09-14 12:30:56,035 - trade - ERROR - Dataframe update failed: No active trade found for SENSEX 18 SEP 82500 PUT to close
2025-09-14 12:30:56,036 - trade - ERROR - Dataframe update failed: No active trade found for SENSEX 18 SEP 81500 CALL to close
2025-09-14 12:30:56,046 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252520000CE
2025-09-14 12:30:56,047 - market_data - INFO - Market is closed, fetching historical price for NIFTY
2025-09-14 12:30:56,046 - trading_dataframes - WARNING - Active trade already exists for NIFTY 16 SEP 25000 PUT. Overwriting with new entry.
2025-09-14 12:30:56,079 - api - INFO - Looking for instrument with trading symbol: SENSEX18SEP20258150000CE
2025-09-14 12:30:56,046 - trade - ERROR - Dataframe update failed: Update received for SENSEX 16 SEP 83000 CALL but no active trade found
2025-09-14 12:30:56,068 - trade - ERROR - Dataframe update failed: Intimation received for SENSEX 16 SEP 82000 CALL but no active trade found
2025-09-14 12:30:56,124 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252480000CE
2025-09-14 12:30:56,137 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-12
2025-09-14 12:30:56,138 - ConcurrentTest - INFO - Worker 8: ❌ UNKNOWN UNKNOWN (0.305s)
2025-09-14 12:30:56,138 - api - INFO - Looking for instrument with trading symbol: SENSEX18SEP20258250000PE
2025-09-14 12:30:56,138 - api - INFO - Looking for instrument with trading symbol: SENSEX18SEP20258150000CE
2025-09-14 12:30:56,153 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-12
2025-09-14 12:30:56,177 - market_data - INFO - Market is closed, fetching historical price for NIFTY
2025-09-14 12:30:56,178 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000PE
2025-09-14 12:30:56,209 - ConcurrentTest - INFO - Worker 12: ❌ UNKNOWN UNKNOWN (0.359s)
2025-09-14 12:30:56,235 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-12
2025-09-14 12:30:56,278 - ConcurrentTest - INFO - Worker 14: ❌ UNKNOWN UNKNOWN (0.373s)
2025-09-14 12:30:56,324 - api - INFO - Exit order placed successfully with ID: 091461a17f3dAO
2025-09-14 12:30:56,324 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Position exited successfully: SELL 20 units', 'order_id': '091461a17f3dAO'}
2025-09-14 12:30:56,346 - ConcurrentTest - INFO - Worker 1: ❌ UNKNOWN UNKNOWN (0.518s)
2025-09-14 12:30:56,338 - trade - ERROR - API signal processing failed: {'status': 'error', 'message': 'No active position found for SENSEX'}
2025-09-14 12:30:56,364 - market_data - INFO - Market is closed, fetching historical price for NIFTY
2025-09-14 12:30:56,346 - trade - ERROR - API signal processing failed: {'status': 'error', 'message': 'No active position found for SENSEX'}
2025-09-14 12:30:56,381 - ConcurrentTest - INFO - Worker 7: ❌ UNKNOWN UNKNOWN (0.550s)
2025-09-14 12:30:56,381 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-12
2025-09-14 12:30:56,381 - trade - ERROR - API signal processing failed: {'status': 'error', 'message': 'No active position found for SENSEX'}
2025-09-14 12:30:56,382 - ConcurrentTest - INFO - Worker 9: ❌ UNKNOWN UNKNOWN (0.548s)
2025-09-14 12:30:56,383 - ConcurrentTest - INFO - Worker 10: ❌ UNKNOWN UNKNOWN (0.548s)
2025-09-14 12:30:56,461 - api - INFO - Exit order placed successfully with ID: 09146f6b0733AO
2025-09-14 12:30:56,461 - api - ERROR - Position exit failed: 'SENSEX'
2025-09-14 12:30:56,461 - trade - ERROR - API signal processing failed: {'status': 'error', 'message': "'SENSEX'"}
2025-09-14 12:30:56,462 - ConcurrentTest - INFO - Worker 11: ❌ UNKNOWN UNKNOWN (0.627s)
2025-09-14 12:30:56,470 - api - INFO - Exit order placed successfully with ID: 091439023b76AO
2025-09-14 12:30:56,470 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Position exited successfully: SELL 75 units', 'order_id': '091439023b76AO'}
2025-09-14 12:30:56,470 - ConcurrentTest - INFO - Worker 0: ❌ UNKNOWN UNKNOWN (0.643s)
2025-09-14 12:30:56,520 - api - INFO - Exit order placed successfully with ID: 0914af2a6a8dAO
2025-09-14 12:30:56,520 - api - ERROR - Position exit failed: 'NIFTY'
2025-09-14 12:30:56,520 - trade - ERROR - API signal processing failed: {'status': 'error', 'message': "'NIFTY'"}
2025-09-14 12:30:56,520 - ConcurrentTest - INFO - Worker 2: ❌ UNKNOWN UNKNOWN (0.692s)
2025-09-14 12:30:56,545 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-11
2025-09-14 12:30:56,547 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-11
2025-09-14 12:30:56,559 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-11
2025-09-14 12:30:56,600 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-11
2025-09-14 12:30:56,635 - api - INFO - Exit order placed successfully with ID: 0914b27887d8AO
2025-09-14 12:30:56,635 - api - ERROR - Position exit failed: 'NIFTY'
2025-09-14 12:30:56,635 - trade - ERROR - API signal processing failed: {'status': 'error', 'message': "'NIFTY'"}
2025-09-14 12:30:56,635 - ConcurrentTest - INFO - Worker 6: ❌ UNKNOWN UNKNOWN (0.804s)
2025-09-14 12:30:56,704 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-10
2025-09-14 12:30:56,710 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-10
2025-09-14 12:30:56,713 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-10
2025-09-14 12:30:56,745 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-10
2025-09-14 12:30:56,859 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-09
2025-09-14 12:30:56,861 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-09
2025-09-14 12:30:56,884 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-09
2025-09-14 12:30:56,896 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-09
2025-09-14 12:30:57,013 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-08
2025-09-14 12:30:57,034 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-08
2025-09-14 12:30:57,040 - market_data - INFO - Attempting to fetch historical data for SENSEX on 2025-09-08
2025-09-14 12:30:57,056 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-08
2025-09-14 12:30:57,158 - market_data - WARNING - No historical price data available for NIFTY after trying multiple dates
2025-09-14 12:30:57,158 - market_data - WARNING - No historical price available for NIFTY
2025-09-14 12:30:57,158 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:30:57,158 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:30:57,159 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 24900 CALL at price 102.62
2025-09-14 12:30:57,159 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 24900 CALL
2025-09-14 12:30:57,159 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252490000CE
2025-09-14 12:30:57,202 - market_data - WARNING - No historical price data available for NIFTY after trying multiple dates
2025-09-14 12:30:57,204 - market_data - WARNING - No historical price data available for SENSEX after trying multiple dates
2025-09-14 12:30:57,204 - market_data - WARNING - No historical price available for NIFTY
2025-09-14 12:30:57,204 - market_data - WARNING - No historical price available for SENSEX
2025-09-14 12:30:57,204 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:30:57,204 - trading_dataframes - WARNING - Could not retrieve market price for SENSEX
2025-09-14 12:30:57,204 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:30:57,204 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:30:57,205 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25200 CALL at price 83.04
2025-09-14 12:30:57,205 - trading_dataframes - INFO - Created new trade entry for SENSEX 18 SEP 83000 CALL at price 136.67
2025-09-14 12:30:57,205 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25200 CALL
2025-09-14 12:30:57,206 - trade - INFO - Dataframe updated: Trade entry created for SENSEX 18 SEP 83000 CALL
2025-09-14 12:30:57,206 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252520000CE
2025-09-14 12:30:57,206 - api - INFO - Looking for instrument with trading symbol: SENSEX18SEP20258300000CE
2025-09-14 12:30:57,255 - market_data - WARNING - No historical price data available for NIFTY after trying multiple dates
2025-09-14 12:30:57,283 - market_data - WARNING - No historical price available for NIFTY
2025-09-14 12:30:57,284 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:30:57,285 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:30:57,286 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 PUT at price 114.95
2025-09-14 12:30:57,286 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 PUT
2025-09-14 12:30:57,286 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000PE
2025-09-14 12:30:57,483 - api - INFO - Executing trade: BUY 75 units (1 lots x 75) of NIFTY16SEP2524900CE
2025-09-14 12:30:57,511 - api - INFO - Executing trade: BUY 75 units (1 lots x 75) of NIFTY16SEP2525000CE
2025-09-14 12:30:57,536 - api - INFO - Executing trade: BUY 75 units (1 lots x 75) of NIFTY16SEP2525200PE
2025-09-14 12:30:57,582 - api - INFO - Executing trade: BUY 20 units (1 lots x 20) of SENSEX2591883000CE
2025-09-14 12:30:57,672 - api - INFO - Order placed successfully with ID: 0914136198fdAO
2025-09-14 12:30:57,672 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '0914136198fdAO', 'price': 250.7, 'quantity': 75, 'lots': 1}
2025-09-14 12:30:57,672 - ConcurrentTest - INFO - Worker 3: ✅ UNKNOWN UNKNOWN (1.843s)
2025-09-14 12:30:57,738 - api - INFO - Order placed successfully with ID: 0914cb4dcb57AO
2025-09-14 12:30:57,738 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '0914cb4dcb57AO', 'price': 109.95, 'quantity': 75, 'lots': 1}
2025-09-14 12:30:57,738 - ConcurrentTest - INFO - Worker 4: ✅ UNKNOWN UNKNOWN (1.908s)
2025-09-14 12:30:57,742 - api - INFO - Order placed successfully with ID: 091428d784e9AO
2025-09-14 12:30:57,742 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '091428d784e9AO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:30:57,742 - ConcurrentTest - INFO - Worker 13: ✅ UNKNOWN UNKNOWN (1.906s)
2025-09-14 12:30:57,772 - api - INFO - Order placed successfully with ID: 09140f910501AO
2025-09-14 12:30:57,773 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 20 units', 'order_id': '09140f910501AO', 'price': 53.5, 'quantity': 20, 'lots': 1}
2025-09-14 12:30:57,773 - ConcurrentTest - INFO - Worker 5: ✅ UNKNOWN UNKNOWN (1.943s)
2025-09-14 12:30:59,778 - ConcurrentTest - INFO - 🔒 Testing database lock resilience with 20 threads...
2025-09-14 12:30:59,781 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:00+05:30
2025-09-14 12:30:59,781 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:00+05:30
2025-09-14 12:30:59,782 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:00+05:30
2025-09-14 12:30:59,783 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:00+05:30
2025-09-14 12:30:59,785 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=165.8, stop_loss=160.2, entry_price=165.8, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:59,787 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:30:59,789 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 12:30:59,785 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:00+05:30
2025-09-14 12:30:59,785 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:00+05:30
2025-09-14 12:30:59,785 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:00+05:30
2025-09-14 12:30:59,785 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=165.8, stop_loss=160.2, entry_price=165.8, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:59,786 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:00+05:30
2025-09-14 12:30:59,826 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:30:59,786 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=165.8, stop_loss=160.2, entry_price=165.8, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:59,787 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:00+05:30
2025-09-14 12:30:59,861 - market_data - INFO - Market is closed, fetching historical price for NIFTY
2025-09-14 12:30:59,860 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 12:30:59,861 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:30:59,787 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:00+05:30
2025-09-14 12:30:59,861 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-12
2025-09-14 12:30:59,787 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=165.8, stop_loss=160.2, entry_price=165.8, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:59,874 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 12:30:59,790 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:00+05:30
2025-09-14 12:30:59,790 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:00+05:30
2025-09-14 12:30:59,804 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:00+05:30
2025-09-14 12:30:59,805 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=165.8, stop_loss=160.2, entry_price=165.8, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:59,902 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:30:59,913 - market_data - INFO - Market is closed, fetching historical price for NIFTY
2025-09-14 12:30:59,817 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:00+05:30
2025-09-14 12:30:59,818 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:00+05:30
2025-09-14 12:30:59,819 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=165.8, stop_loss=160.2, entry_price=165.8, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:59,941 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 12:30:59,826 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:00+05:30
2025-09-14 12:30:59,941 - market_data - INFO - Market is closed, fetching historical price for NIFTY
2025-09-14 12:30:59,930 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:30:59,942 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-12
2025-09-14 12:30:59,826 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=165.8, stop_loss=160.2, entry_price=165.8, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:59,826 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:00+05:30
2025-09-14 12:30:59,827 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=165.8, stop_loss=160.2, entry_price=165.8, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:59,827 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:00+05:30
2025-09-14 12:30:59,942 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:30:59,833 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:00+05:30
2025-09-14 12:30:59,851 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:00+05:30
2025-09-14 12:30:59,980 - market_data - INFO - Market is closed, fetching historical price for NIFTY
2025-09-14 12:30:59,861 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=165.8, stop_loss=160.2, entry_price=165.8, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:59,898 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=165.8, stop_loss=160.2, entry_price=165.8, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:59,981 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 12:30:59,981 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:30:59,980 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-12
2025-09-14 12:30:59,913 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=165.8, stop_loss=160.2, entry_price=165.8, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:59,917 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=165.8, stop_loss=160.2, entry_price=165.8, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:59,924 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=165.8, stop_loss=160.2, entry_price=165.8, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:59,942 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=165.8, stop_loss=160.2, entry_price=165.8, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:59,942 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=165.8, stop_loss=160.2, entry_price=165.8, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:59,980 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=165.8, stop_loss=160.2, entry_price=165.8, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:59,983 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:30:59,982 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=165.8, stop_loss=160.2, entry_price=165.8, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:59,983 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=165.8, stop_loss=160.2, entry_price=165.8, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:59,984 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 12:30:59,984 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=165.8, stop_loss=160.2, entry_price=165.8, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:59,984 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-12
2025-09-14 12:30:59,984 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=165.8, stop_loss=160.2, entry_price=165.8, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:59,984 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:30:59,984 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:30:59,999 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 12:31:00,024 - market_data - INFO - Market is closed, fetching historical price for NIFTY
2025-09-14 12:31:00,024 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:31:00,024 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:31:00,024 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:31:00,024 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:31:00,024 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:31:00,025 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:31:00,025 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 12:31:00,025 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:31:00,025 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:31:00,067 - market_data - INFO - Market is closed, fetching historical price for NIFTY
2025-09-14 12:31:00,031 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:31:00,067 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 12:31:00,066 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:31:00,068 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 12:31:00,090 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-12
2025-09-14 12:31:00,090 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 12:31:00,103 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 12:31:00,104 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 12:31:00,112 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 12:31:00,112 - market_data - INFO - Market is closed, fetching historical price for NIFTY
2025-09-14 12:31:00,113 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 12:31:00,113 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 12:31:00,120 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 12:31:00,127 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 12:31:00,127 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-12
2025-09-14 12:31:00,128 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 12:31:00,168 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-11
2025-09-14 12:31:00,195 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 12:31:00,184 - market_data - INFO - Market is closed, fetching historical price for NIFTY
2025-09-14 12:31:00,195 - market_data - INFO - Market is closed, fetching historical price for NIFTY
2025-09-14 12:31:00,293 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-12
2025-09-14 12:31:00,303 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-11
2025-09-14 12:31:00,330 - market_data - INFO - Market is closed, fetching historical price for NIFTY
2025-09-14 12:31:00,386 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-11
2025-09-14 12:31:00,423 - market_data - INFO - Market is closed, fetching historical price for NIFTY
2025-09-14 12:31:00,424 - market_data - INFO - Market is closed, fetching historical price for NIFTY
2025-09-14 12:31:00,441 - market_data - INFO - Market is closed, fetching historical price for NIFTY
2025-09-14 12:31:00,478 - market_data - INFO - Market is closed, fetching historical price for NIFTY
2025-09-14 12:31:00,518 - market_data - INFO - Market is closed, fetching historical price for NIFTY
2025-09-14 12:31:00,539 - market_data - INFO - Market is closed, fetching historical price for NIFTY
2025-09-14 12:31:00,540 - market_data - INFO - Market is closed, fetching historical price for NIFTY
2025-09-14 12:31:00,540 - market_data - INFO - Market is closed, fetching historical price for NIFTY
2025-09-14 12:31:00,542 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-11
2025-09-14 12:31:00,598 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-12
2025-09-14 12:31:00,599 - market_data - INFO - Market is closed, fetching historical price for NIFTY
2025-09-14 12:31:00,599 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-12
2025-09-14 12:31:00,622 - market_data - INFO - Market is closed, fetching historical price for NIFTY
2025-09-14 12:31:00,624 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-12
2025-09-14 12:31:00,626 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-11
2025-09-14 12:31:00,627 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-12
2025-09-14 12:31:00,627 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-12
2025-09-14 12:31:00,627 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-12
2025-09-14 12:31:00,627 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-12
2025-09-14 12:31:00,628 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-12
2025-09-14 12:31:00,628 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-12
2025-09-14 12:31:00,628 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-12
2025-09-14 12:31:00,628 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-12
2025-09-14 12:31:00,629 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-12
2025-09-14 12:31:00,630 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-12
2025-09-14 12:31:00,706 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-11
2025-09-14 12:31:00,866 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-10
2025-09-14 12:31:01,492 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-10
2025-09-14 12:31:01,604 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-10
2025-09-14 12:31:01,611 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-11
2025-09-14 12:31:01,618 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-11
2025-09-14 12:31:01,620 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-10
2025-09-14 12:31:01,622 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-11
2025-09-14 12:31:01,630 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-10
2025-09-14 12:31:01,634 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-11
2025-09-14 12:31:01,637 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-11
2025-09-14 12:31:01,651 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-11
2025-09-14 12:31:01,655 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-11
2025-09-14 12:31:01,658 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-10
2025-09-14 12:31:01,665 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-11
2025-09-14 12:31:01,675 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-11
2025-09-14 12:31:01,683 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-11
2025-09-14 12:31:01,693 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-11
2025-09-14 12:31:01,700 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-09
2025-09-14 12:31:01,711 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-11
2025-09-14 12:31:01,739 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-09
2025-09-14 12:31:01,748 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-11
2025-09-14 12:31:01,753 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-11
2025-09-14 12:31:02,160 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-09
2025-09-14 12:31:02,307 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-10
2025-09-14 12:31:02,332 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-09
2025-09-14 12:31:02,419 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-10
2025-09-14 12:31:02,458 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-10
2025-09-14 12:31:02,524 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-09
2025-09-14 12:31:02,549 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-10
2025-09-14 12:31:02,598 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-10
2025-09-14 12:31:02,605 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-10
2025-09-14 12:31:02,629 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-10
2025-09-14 12:31:02,640 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-10
2025-09-14 12:31:02,648 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-10
2025-09-14 12:31:02,654 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-09
2025-09-14 12:31:02,674 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-10
2025-09-14 12:31:02,678 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-10
2025-09-14 12:31:02,683 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-08
2025-09-14 12:31:02,710 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-10
2025-09-14 12:31:02,717 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-10
2025-09-14 12:31:02,720 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-08
2025-09-14 12:31:02,779 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-10
2025-09-14 12:31:02,818 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-08
2025-09-14 12:31:02,923 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-09
2025-09-14 12:31:03,061 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-09
2025-09-14 12:31:03,101 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-08
2025-09-14 12:31:03,137 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-09
2025-09-14 12:31:03,182 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-08
2025-09-14 12:31:03,230 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-09
2025-09-14 12:31:03,235 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-09
2025-09-14 12:31:03,239 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-09
2025-09-14 12:31:03,242 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-09
2025-09-14 12:31:03,245 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-09
2025-09-14 12:31:03,248 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-08
2025-09-14 12:31:03,254 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-09
2025-09-14 12:31:03,259 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-09
2025-09-14 12:31:03,303 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-09
2025-09-14 12:31:03,306 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-09
2025-09-14 12:31:03,310 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-08
2025-09-14 12:31:03,313 - market_data - WARNING - No historical price data available for NIFTY after trying multiple dates
2025-09-14 12:31:03,318 - market_data - WARNING - No historical price data available for NIFTY after trying multiple dates
2025-09-14 12:31:03,320 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-09
2025-09-14 12:31:03,322 - market_data - WARNING - No historical price data available for NIFTY after trying multiple dates
2025-09-14 12:31:03,325 - market_data - WARNING - No historical price available for NIFTY
2025-09-14 12:31:03,325 - market_data - WARNING - No historical price available for NIFTY
2025-09-14 12:31:03,327 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:31:03,326 - market_data - WARNING - No historical price available for NIFTY
2025-09-14 12:31:03,327 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:31:03,327 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:31:03,327 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:31:03,329 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:31:03,330 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 165.8
2025-09-14 12:31:03,330 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:31:03,330 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 165.8
2025-09-14 12:31:03,331 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 12:31:03,331 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 165.8
2025-09-14 12:31:03,331 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 12:31:03,332 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 12:31:03,332 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 12:31:03,332 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 12:31:03,333 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 12:31:03,354 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-08
2025-09-14 12:31:03,450 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-09
2025-09-14 12:31:03,450 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-08
2025-09-14 12:31:03,515 - market_data - WARNING - No historical price data available for NIFTY after trying multiple dates
2025-09-14 12:31:03,535 - market_data - WARNING - No historical price available for NIFTY
2025-09-14 12:31:03,559 - market_data - WARNING - No historical price data available for NIFTY after trying multiple dates
2025-09-14 12:31:03,580 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:31:03,582 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:31:03,580 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-08
2025-09-14 12:31:03,582 - market_data - WARNING - No historical price available for NIFTY
2025-09-14 12:31:03,586 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 165.8
2025-09-14 12:31:03,589 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:31:03,590 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 12:31:03,590 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:31:03,590 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 12:31:03,591 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 165.8
2025-09-14 12:31:03,635 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-08
2025-09-14 12:31:03,607 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 12:31:03,648 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 12:31:03,637 - market_data - WARNING - No historical price data available for NIFTY after trying multiple dates
2025-09-14 12:31:03,640 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-08
2025-09-14 12:31:03,686 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-08
2025-09-14 12:31:03,692 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-08
2025-09-14 12:31:03,699 - market_data - WARNING - No historical price available for NIFTY
2025-09-14 12:31:03,721 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-08
2025-09-14 12:31:03,734 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-08
2025-09-14 12:31:03,735 - market_data - WARNING - No historical price data available for NIFTY after trying multiple dates
2025-09-14 12:31:03,738 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-08
2025-09-14 12:31:03,770 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:31:03,748 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-08
2025-09-14 12:31:03,764 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-08
2025-09-14 12:31:03,775 - market_data - WARNING - No historical price available for NIFTY
2025-09-14 12:31:03,777 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:31:03,782 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:31:03,784 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 165.8
2025-09-14 12:31:03,784 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:31:03,784 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 12:31:03,786 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 165.8
2025-09-14 12:31:03,786 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 12:31:03,787 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 12:31:03,806 - market_data - INFO - Attempting to fetch historical data for NIFTY on 2025-09-08
2025-09-14 12:31:03,821 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 12:31:03,951 - api - INFO - Executing trade: BUY 75 units (1 lots x 75) of NIFTY16SEP2525000CE
2025-09-14 12:31:03,972 - market_data - WARNING - No historical price data available for NIFTY after trying multiple dates
2025-09-14 12:31:03,972 - market_data - WARNING - No historical price available for NIFTY
2025-09-14 12:31:03,972 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:31:03,972 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:31:03,973 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 165.8
2025-09-14 12:31:03,973 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 12:31:03,973 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 12:31:04,044 - api - INFO - Executing trade: BUY 75 units (1 lots x 75) of NIFTY16SEP2525000CE
2025-09-14 12:31:04,099 - market_data - WARNING - No historical price data available for NIFTY after trying multiple dates
2025-09-14 12:31:04,100 - market_data - WARNING - No historical price available for NIFTY
2025-09-14 12:31:04,100 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:31:04,100 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:31:04,100 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 165.8
2025-09-14 12:31:04,101 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 12:31:04,101 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 12:31:04,129 - market_data - WARNING - No historical price data available for NIFTY after trying multiple dates
2025-09-14 12:31:04,129 - market_data - WARNING - No historical price available for NIFTY
2025-09-14 12:31:04,129 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:31:04,130 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:31:04,130 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 165.8
2025-09-14 12:31:04,130 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 12:31:04,130 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 12:31:04,154 - api - INFO - Executing trade: BUY 75 units (1 lots x 75) of NIFTY16SEP2525000CE
2025-09-14 12:31:04,510 - market_data - WARNING - No historical price data available for NIFTY after trying multiple dates
2025-09-14 12:31:04,510 - market_data - WARNING - No historical price available for NIFTY
2025-09-14 12:31:04,510 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:31:04,510 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:31:04,511 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 165.8
2025-09-14 12:31:04,515 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 12:31:04,515 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 12:31:04,514 - market_data - WARNING - No historical price data available for NIFTY after trying multiple dates
2025-09-14 12:31:04,546 - market_data - WARNING - No historical price available for NIFTY
2025-09-14 12:31:04,547 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:31:04,547 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:31:04,567 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 165.8
2025-09-14 12:31:04,567 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 12:31:04,567 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 12:31:04,602 - market_data - WARNING - No historical price data available for NIFTY after trying multiple dates
2025-09-14 12:31:04,602 - market_data - WARNING - No historical price available for NIFTY
2025-09-14 12:31:04,627 - market_data - WARNING - No historical price data available for NIFTY after trying multiple dates
2025-09-14 12:31:04,638 - market_data - WARNING - No historical price data available for NIFTY after trying multiple dates
2025-09-14 12:31:04,645 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:31:04,645 - market_data - WARNING - No historical price available for NIFTY
2025-09-14 12:31:04,647 - market_data - WARNING - No historical price data available for NIFTY after trying multiple dates
2025-09-14 12:31:04,671 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:31:04,656 - market_data - WARNING - No historical price data available for NIFTY after trying multiple dates
2025-09-14 12:31:04,667 - market_data - WARNING - No historical price data available for NIFTY after trying multiple dates
2025-09-14 12:31:04,671 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:31:04,669 - market_data - WARNING - No historical price data available for NIFTY after trying multiple dates
2025-09-14 12:31:04,671 - market_data - WARNING - No historical price available for NIFTY
2025-09-14 12:31:04,672 - market_data - WARNING - No historical price available for NIFTY
2025-09-14 12:31:04,672 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 165.8
2025-09-14 12:31:04,673 - market_data - WARNING - No historical price available for NIFTY
2025-09-14 12:31:04,673 - market_data - WARNING - No historical price available for NIFTY
2025-09-14 12:31:04,673 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:31:04,673 - market_data - WARNING - No historical price available for NIFTY
2025-09-14 12:31:04,673 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:31:04,673 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 12:31:04,673 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:31:04,673 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:31:04,673 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:31:04,674 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 165.8
2025-09-14 12:31:04,674 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:31:04,674 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:31:04,674 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 12:31:04,674 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:31:04,674 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:31:04,674 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:31:04,675 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 12:31:04,675 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:31:04,675 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 165.8
2025-09-14 12:31:04,681 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 165.8
2025-09-14 12:31:04,688 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 165.8
2025-09-14 12:31:04,692 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 165.8
2025-09-14 12:31:04,712 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 12:31:04,716 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 165.8
2025-09-14 12:31:04,718 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 12:31:04,718 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 12:31:04,719 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 12:31:04,722 - api - INFO - Executing trade: BUY 75 units (1 lots x 75) of NIFTY16SEP2525000CE
2025-09-14 12:31:04,720 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 12:31:04,735 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 12:31:04,738 - api - INFO - Executing trade: BUY 75 units (1 lots x 75) of NIFTY16SEP2525000CE
2025-09-14 12:31:04,742 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 12:31:04,749 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 12:31:04,760 - api - INFO - Executing trade: BUY 75 units (1 lots x 75) of NIFTY16SEP2525000CE
2025-09-14 12:31:04,760 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 12:31:04,771 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 12:31:04,774 - api - INFO - Order placed successfully with ID: 0914b5425f06AO
2025-09-14 12:31:04,775 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 12:31:04,806 - api - INFO - Executing trade: BUY 75 units (1 lots x 75) of NIFTY16SEP2525000CE
2025-09-14 12:31:04,814 - api - INFO - Executing trade: BUY 75 units (1 lots x 75) of NIFTY16SEP2525000CE
2025-09-14 12:31:04,846 - api - INFO - Order placed successfully with ID: 0914e2ee3c7dAO
2025-09-14 12:31:04,866 - api - INFO - Executing trade: BUY 75 units (1 lots x 75) of NIFTY16SEP2525000CE
2025-09-14 12:31:04,889 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '0914b5425f06AO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:31:04,881 - api - INFO - Order placed successfully with ID: 0914929da5b4AO
2025-09-14 12:31:04,906 - api - INFO - Executing trade: BUY 75 units (1 lots x 75) of NIFTY16SEP2525000CE
2025-09-14 12:31:04,957 - api - ERROR - Error fetching market data: Couldn't parse the JSON response received from the server: b'Access denied because of exceeding access rate'
2025-09-14 12:31:04,987 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '0914e2ee3c7dAO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:31:04,979 - api - INFO - Executing trade: BUY 75 units (1 lots x 75) of NIFTY16SEP2525000CE
2025-09-14 12:31:05,007 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '0914929da5b4AO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:31:05,010 - trade - ERROR - API signal processing failed: {'status': 'warning', 'message': 'Unable to fetch live market data for NIFTY. Signal logged only.'}
2025-09-14 12:31:05,011 - ConcurrentTest - INFO - Worker 3: ✅ UNKNOWN UNKNOWN (5.230s)
2025-09-14 12:31:05,013 - ConcurrentTest - INFO - Worker 1: ✅ UNKNOWN UNKNOWN (5.232s)
2025-09-14 12:31:05,013 - ConcurrentTest - INFO - Worker 0: ✅ UNKNOWN UNKNOWN (5.233s)
2025-09-14 12:31:05,014 - ConcurrentTest - INFO - Worker 14: ✅ UNKNOWN UNKNOWN (5.196s)
2025-09-14 12:31:05,081 - api - INFO - Executing trade: BUY 75 units (1 lots x 75) of NIFTY16SEP2525000CE
2025-09-14 12:31:05,095 - api - INFO - Executing trade: BUY 75 units (1 lots x 75) of NIFTY16SEP2525000CE
2025-09-14 12:31:05,109 - api - INFO - Order placed successfully with ID: 09146fff62a2AO
2025-09-14 12:31:05,109 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '09146fff62a2AO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:31:05,109 - ConcurrentTest - INFO - Worker 2: ✅ UNKNOWN UNKNOWN (5.329s)
2025-09-14 12:31:05,133 - api - INFO - Order placed successfully with ID: 091466cf67b3AO
2025-09-14 12:31:05,134 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '091466cf67b3AO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:31:05,134 - ConcurrentTest - INFO - Worker 4: ✅ UNKNOWN UNKNOWN (5.352s)
2025-09-14 12:31:05,145 - api - INFO - Order placed successfully with ID: 09144fe99453AO
2025-09-14 12:31:05,145 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '09144fe99453AO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:31:05,145 - ConcurrentTest - INFO - Worker 8: ✅ UNKNOWN UNKNOWN (5.360s)
2025-09-14 12:31:05,363 - api - INFO - Executing trade: BUY 75 units (1 lots x 75) of NIFTY16SEP2525000CE
2025-09-14 12:31:05,483 - api - INFO - Executing trade: BUY 75 units (1 lots x 75) of NIFTY16SEP2525000CE
2025-09-14 12:31:05,541 - api - INFO - Order placed successfully with ID: 0914ef4a163fAO
2025-09-14 12:31:05,542 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '0914ef4a163fAO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:31:05,542 - ConcurrentTest - INFO - Worker 13: ✅ UNKNOWN UNKNOWN (5.737s)
2025-09-14 12:31:05,568 - api - INFO - Order placed successfully with ID: 09149b70dacfAO
2025-09-14 12:31:05,569 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '09149b70dacfAO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:31:05,569 - ConcurrentTest - INFO - Worker 5: ✅ UNKNOWN UNKNOWN (5.787s)
2025-09-14 12:31:05,655 - api - INFO - Executing trade: BUY 75 units (1 lots x 75) of NIFTY16SEP2525000CE
2025-09-14 12:31:05,670 - api - INFO - Executing trade: BUY 75 units (1 lots x 75) of NIFTY16SEP2525000CE
2025-09-14 12:31:05,676 - api - INFO - Executing trade: BUY 75 units (1 lots x 75) of NIFTY16SEP2525000CE
2025-09-14 12:31:05,689 - api - INFO - Order placed successfully with ID: 091430dd6cfaAO
2025-09-14 12:31:05,689 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '091430dd6cfaAO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:31:05,690 - ConcurrentTest - INFO - Worker 7: ✅ UNKNOWN UNKNOWN (5.905s)
2025-09-14 12:31:05,692 - api - INFO - Order placed successfully with ID: 091422714471AO
2025-09-14 12:31:05,692 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '091422714471AO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:31:05,695 - api - INFO - Order placed successfully with ID: 0914601d2a28AO
2025-09-14 12:31:05,695 - ConcurrentTest - INFO - Worker 6: ✅ UNKNOWN UNKNOWN (5.911s)
2025-09-14 12:31:05,695 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '0914601d2a28AO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:31:05,695 - ConcurrentTest - INFO - Worker 11: ✅ UNKNOWN UNKNOWN (5.908s)
2025-09-14 12:31:05,713 - api - INFO - Order placed successfully with ID: 09140c66fbbdAO
2025-09-14 12:31:05,713 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '09140c66fbbdAO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:31:05,713 - ConcurrentTest - INFO - Worker 18: ✅ UNKNOWN UNKNOWN (5.887s)
2025-09-14 12:31:05,731 - api - INFO - Order placed successfully with ID: 09147b1f68e6AO
2025-09-14 12:31:05,732 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '09147b1f68e6AO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:31:05,733 - ConcurrentTest - INFO - Worker 16: ✅ UNKNOWN UNKNOWN (5.907s)
2025-09-14 12:31:05,742 - api - INFO - Order placed successfully with ID: 0914f4f78286AO
2025-09-14 12:31:05,742 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '0914f4f78286AO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:31:05,742 - ConcurrentTest - INFO - Worker 17: ✅ UNKNOWN UNKNOWN (5.916s)
2025-09-14 12:31:05,766 - api - INFO - Order placed successfully with ID: 0914f7fcf274AO
2025-09-14 12:31:05,767 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '0914f7fcf274AO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:31:05,767 - ConcurrentTest - INFO - Worker 12: ✅ UNKNOWN UNKNOWN (5.977s)
2025-09-14 12:31:05,865 - api - INFO - Order placed successfully with ID: 09140892f10cAO
2025-09-14 12:31:05,866 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '09140892f10cAO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:31:05,866 - ConcurrentTest - INFO - Worker 15: ✅ UNKNOWN UNKNOWN (6.047s)
2025-09-14 12:31:05,886 - api - INFO - Order placed successfully with ID: 09148ec1feb3AO
2025-09-14 12:31:05,887 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '09148ec1feb3AO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:31:05,887 - ConcurrentTest - INFO - Worker 10: ✅ UNKNOWN UNKNOWN (6.100s)
2025-09-14 12:31:05,905 - api - INFO - Order placed successfully with ID: 0914a1f36114AO
2025-09-14 12:31:05,905 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '0914a1f36114AO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:31:05,906 - ConcurrentTest - INFO - Worker 9: ✅ UNKNOWN UNKNOWN (6.119s)
2025-09-14 12:31:07,135 - market_data - WARNING - No historical price data available for NIFTY after trying multiple dates
2025-09-14 12:31:07,135 - market_data - WARNING - No historical price available for NIFTY
2025-09-14 12:31:07,135 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:31:07,136 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:31:07,138 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 165.8
2025-09-14 12:31:07,138 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 12:31:07,138 - api - INFO - Looking for instrument with trading symbol: NIFTY16SEP20252500000CE
2025-09-14 12:31:07,385 - api - INFO - Executing trade: BUY 75 units (1 lots x 75) of NIFTY16SEP2525000CE
2025-09-14 12:31:07,585 - api - INFO - Order placed successfully with ID: 0914fc286e6eAO
2025-09-14 12:31:07,586 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '0914fc286e6eAO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:31:07,586 - ConcurrentTest - INFO - Worker 19: ✅ UNKNOWN UNKNOWN (7.759s)
