#!/usr/bin/env python3
"""
Concurrent Execution Test for Trading Bot
Simulates high-frequency trading scenarios with multiple simultaneous signals
Tests race conditions, database locks, and system stability under load
"""

import asyncio
import threading
import time
import random
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any
import os
import sys
import json

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.signal_handler import SignalHandler
from utils.api_handler import APIHandler
from utils.trading_dataframe_manager import TradingDataframeManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('tests/concurrent_test.log'),
        logging.StreamHandler()
    ]
)

class ConcurrentExecutionTest:
    """
    Test concurrent execution capabilities of the trading bot
    """
    
    def __init__(self):
        self.logger = logging.getLogger('ConcurrentTest')
        self.results = []
        self.lock = threading.Lock()

        # Initialize handlers
        self.signal_handler = SignalHandler()
        self.api_handler = self.signal_handler.api
        self.dataframe_manager = self.signal_handler.dataframe_manager
        
        # Test signal templates
        self.signal_templates = {
            "nifty_buy": "===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP {strike} {option_type}${price}${stop_loss}$2025-09-12 {time}+05:30",
            "sensex_buy": "===Algo_Trading===$TRADE$BUY$SENSEX 18 SEP {strike} {option_type}${price}${stop_loss}$2025-09-12 {time}+05:30",
            "nifty_close": "===Algo_Trading===$TRADE$CLOSE$NIFTY 16 SEP {strike} {option_type}${price}$2025-09-12 {time}+05:30$profit_booking${price}",
            "sensex_close": "===Algo_Trading===$TRADE$CLOSE$SENSEX 18 SEP {strike} {option_type}${price}$2025-09-12 {time}+05:30$profit_booking${price}",
            "intimation": "===Algo_Trading===$INTIMATION$Continue to hold trade for ${symbol} 16 SEP {strike} {option_type} at current price: {price}",
            "update": "===Algo_Trading===$Update$STOP LOSS to${new_sl}$for option${symbol} 16 SEP {strike} {option_type}"
        }
    
    def generate_random_signal(self, signal_type: str = None) -> str:
        """Generate a random trading signal"""
        if not signal_type:
            signal_type = random.choice(list(self.signal_templates.keys()))
        
        template = self.signal_templates[signal_type]
        
        # Random parameters
        nifty_strikes = [24800, 24900, 25000, 25100, 25200]
        sensex_strikes = [81000, 81500, 82000, 82500, 83000]
        option_types = ["CALL", "PUT"]
        
        if "nifty" in signal_type.lower():
            strike = random.choice(nifty_strikes)
            symbol = "NIFTY"
        else:
            strike = random.choice(sensex_strikes)
            symbol = "SENSEX"
        
        option_type = random.choice(option_types)
        price = round(random.uniform(50, 200), 2)
        stop_loss = round(price * 0.95, 2)
        new_sl = round(price * 0.98, 2)
        
        # Generate time
        hour = random.randint(9, 15)
        minute = random.randint(0, 59)
        second = random.randint(0, 59)
        time_str = f"{hour:02d}:{minute:02d}:{second:02d}"
        
        return template.format(
            strike=strike,
            option_type=option_type,
            price=price,
            stop_loss=stop_loss,
            time=time_str,
            symbol=symbol,
            new_sl=new_sl
        )
    
    def process_signal_worker(self, worker_id: int, signal: str, delay: float = 0):
        """Worker function to process a single signal"""
        if delay > 0:
            time.sleep(delay)
        
        start_time = time.time()
        result = {
            "worker_id": worker_id,
            "signal": signal[:80] + "..." if len(signal) > 80 else signal,
            "start_time": start_time,
            "success": False,
            "processing_time": 0,
            "errors": []
        }
        
        try:
            # Process signal through signal handler (async)
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            processing_result = loop.run_until_complete(
                self.signal_handler.handle_telegram_message(signal)
            )
            loop.close()

            if processing_result and processing_result.get('status') == 'success':
                result["success"] = True
                result["symbol"] = processing_result.get('symbol', 'UNKNOWN')
                result["action"] = processing_result.get('action', 'UNKNOWN')
                result["message"] = processing_result.get('message', '')
            else:
                result["errors"].append(f"Signal processing failed: {processing_result.get('message', 'Unknown error') if processing_result else 'No response'}")
                result["success"] = False
            
        except Exception as e:
            result["errors"].append(f"Exception: {str(e)}")
        
        finally:
            result["processing_time"] = time.time() - start_time
            
            # Thread-safe result storage
            with self.lock:
                self.results.append(result)
                self.logger.info(f"Worker {worker_id}: {'✅' if result['success'] else '❌'} {result.get('symbol', 'UNKNOWN')} {result.get('action', 'UNKNOWN')} ({result['processing_time']:.3f}s)")
        
        return result
    
    def test_concurrent_buy_signals(self, num_signals: int = 10) -> Dict[str, Any]:
        """Test concurrent BUY signals"""
        self.logger.info(f"🚀 Testing {num_signals} concurrent BUY signals...")
        
        # Generate BUY signals
        signals = []
        for i in range(num_signals):
            signal_type = "nifty_buy" if i % 2 == 0 else "sensex_buy"
            signals.append(self.generate_random_signal(signal_type))
        
        # Launch concurrent threads
        threads = []
        start_time = time.time()
        
        for i, signal in enumerate(signals):
            thread = threading.Thread(
                target=self.process_signal_worker,
                args=(i, signal, random.uniform(0, 0.5))  # Random delay up to 0.5s
            )
            threads.append(thread)
            thread.start()
        
        # Wait for completion
        for thread in threads:
            thread.join(timeout=30)
        
        total_time = time.time() - start_time
        
        # Analyze results
        successful = len([r for r in self.results if r.get('success', False)])
        
        return {
            "test_type": "concurrent_buy_signals",
            "total_signals": num_signals,
            "successful_signals": successful,
            "failed_signals": num_signals - successful,
            "total_time": total_time,
            "average_processing_time": sum(r.get('processing_time', 0) for r in self.results) / len(self.results) if self.results else 0,
            "success_rate": (successful / num_signals) * 100 if num_signals > 0 else 0
        }
    
    def test_mixed_signal_burst(self, num_signals: int = 15) -> Dict[str, Any]:
        """Test burst of mixed signal types"""
        self.logger.info(f"💥 Testing burst of {num_signals} mixed signals...")
        
        self.results.clear()  # Clear previous results
        
        # Generate mixed signals
        signals = []
        signal_types = list(self.signal_templates.keys())
        
        for i in range(num_signals):
            signal_type = random.choice(signal_types)
            signals.append(self.generate_random_signal(signal_type))
        
        # Launch all signals simultaneously
        threads = []
        start_time = time.time()
        
        for i, signal in enumerate(signals):
            thread = threading.Thread(
                target=self.process_signal_worker,
                args=(i, signal, 0)  # No delay for burst test
            )
            threads.append(thread)
            thread.start()
        
        # Wait for completion
        for thread in threads:
            thread.join(timeout=45)
        
        total_time = time.time() - start_time
        
        # Analyze results by signal type
        signal_analysis = {}
        for result in self.results:
            action = result.get('action', 'UNKNOWN')
            if action not in signal_analysis:
                signal_analysis[action] = {"total": 0, "successful": 0}
            signal_analysis[action]["total"] += 1
            if result.get('success', False):
                signal_analysis[action]["successful"] += 1
        
        successful = len([r for r in self.results if r.get('success', False)])
        
        return {
            "test_type": "mixed_signal_burst",
            "total_signals": num_signals,
            "successful_signals": successful,
            "failed_signals": num_signals - successful,
            "total_time": total_time,
            "signal_analysis": signal_analysis,
            "success_rate": (successful / num_signals) * 100 if num_signals > 0 else 0,
            "errors_encountered": [r.get('errors', []) for r in self.results if r.get('errors')]
        }
    
    def test_database_lock_resilience(self, num_threads: int = 20) -> Dict[str, Any]:
        """Test resilience against database locks"""
        self.logger.info(f"🔒 Testing database lock resilience with {num_threads} threads...")
        
        self.results.clear()
        
        # Create signals that will all try to access the same instrument
        signals = []
        for i in range(num_threads):
            # All signals for the same instrument to maximize lock contention
            signal = "===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:00+05:30"
            signals.append(signal)
        
        # Launch all threads simultaneously
        threads = []
        start_time = time.time()
        
        for i, signal in enumerate(signals):
            thread = threading.Thread(
                target=self.process_signal_worker,
                args=(i, signal, 0)
            )
            threads.append(thread)
            thread.start()
        
        # Wait for completion
        for thread in threads:
            thread.join(timeout=60)
        
        total_time = time.time() - start_time
        successful = len([r for r in self.results if r.get('success', False)])
        
        # Check for database lock errors
        lock_errors = []
        for result in self.results:
            for error in result.get('errors', []):
                if 'lock' in error.lower() or 'database' in error.lower():
                    lock_errors.append(error)
        
        return {
            "test_type": "database_lock_resilience",
            "total_threads": num_threads,
            "successful_threads": successful,
            "failed_threads": num_threads - successful,
            "total_time": total_time,
            "lock_errors_found": len(lock_errors),
            "lock_errors": lock_errors[:5],  # First 5 errors
            "success_rate": (successful / num_threads) * 100 if num_threads > 0 else 0
        }
    
    def run_all_concurrent_tests(self) -> Dict[str, Any]:
        """Run all concurrent execution tests"""
        self.logger.info("🎯 Starting comprehensive concurrent execution tests...")
        
        test_results = {
            "test_start_time": datetime.now().isoformat(),
            "tests": {}
        }
        
        # Test 1: Concurrent BUY signals
        test_results["tests"]["concurrent_buy"] = self.test_concurrent_buy_signals(10)
        time.sleep(2)  # Brief pause between tests
        
        # Test 2: Mixed signal burst
        test_results["tests"]["mixed_burst"] = self.test_mixed_signal_burst(15)
        time.sleep(2)
        
        # Test 3: Database lock resilience
        test_results["tests"]["database_locks"] = self.test_database_lock_resilience(20)
        
        test_results["test_end_time"] = datetime.now().isoformat()
        
        # Generate overall summary
        total_signals = sum(test["total_signals"] if "total_signals" in test else test.get("total_threads", 0) for test in test_results["tests"].values())
        total_successful = sum(test["successful_signals"] if "successful_signals" in test else test.get("successful_threads", 0) for test in test_results["tests"].values())
        
        test_results["summary"] = {
            "total_signals_processed": total_signals,
            "total_successful": total_successful,
            "overall_success_rate": (total_successful / total_signals * 100) if total_signals > 0 else 0,
            "system_stability": "STABLE" if total_successful / total_signals > 0.9 else "UNSTABLE" if total_successful / total_signals > 0.7 else "CRITICAL"
        }
        
        return test_results


def main():
    """Main execution function"""
    print("🚀 Starting Concurrent Execution Test Suite")
    print("=" * 50)
    
    # Create test instance
    test_suite = ConcurrentExecutionTest()
    
    # Run all concurrent tests
    results = test_suite.run_all_concurrent_tests()
    
    # Print summary
    summary = results["summary"]
    print(f"\n📊 CONCURRENT EXECUTION TEST SUMMARY")
    print(f"Total Signals Processed: {summary['total_signals_processed']}")
    print(f"Successful: {summary['total_successful']}")
    print(f"Success Rate: {summary['overall_success_rate']:.1f}%")
    print(f"System Stability: {summary['system_stability']}")
    
    # Print individual test results
    for test_name, test_result in results["tests"].items():
        print(f"\n{test_name.upper()}:")
        print(f"  Success Rate: {test_result.get('success_rate', 0):.1f}%")
        if 'lock_errors_found' in test_result:
            print(f"  Lock Errors: {test_result['lock_errors_found']}")
    
    # Save results
    with open('tests/concurrent_test_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n📄 Detailed results saved to: tests/concurrent_test_results.json")
    print("🏁 Concurrent execution tests completed!")


if __name__ == "__main__":
    main()
