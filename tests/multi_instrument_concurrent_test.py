#!/usr/bin/env python3
"""
Multi-Instrument Concurrent Test
Tests concurrent execution with multiple instruments (NIFTY and SENSEX)
"""

import asyncio
import threading
import time
import logging
from datetime import datetime
import os
import sys
import json

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.signal_handler import SignalHandler

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('tests/multi_instrument_test.log'),
        logging.StreamHandler()
    ]
)

class MultiInstrumentConcurrentTest:
    """Test concurrent execution with multiple instruments"""
    
    def __init__(self):
        self.logger = logging.getLogger('MultiInstrumentTest')
        self.results = []
        self.lock = threading.Lock()
        self.signal_handler = SignalHandler()
    
    def process_signal_async(self, worker_id: int, signal: str, instrument: str):
        """Process a single signal asynchronously"""
        start_time = time.time()
        result = {
            "worker_id": worker_id,
            "instrument": instrument,
            "signal": signal[:80] + "..." if len(signal) > 80 else signal,
            "start_time": start_time,
            "success": False,
            "processing_time": 0,
            "errors": [],
            "order_id": None
        }
        
        try:
            # Create new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # Process signal
            processing_result = loop.run_until_complete(
                self.signal_handler.handle_telegram_message(signal)
            )
            loop.close()
            
            if processing_result and processing_result.get('status') == 'success':
                result["success"] = True
                result["symbol"] = processing_result.get('symbol', instrument)
                result["action"] = processing_result.get('action', 'UNKNOWN')
                result["message"] = processing_result.get('message', '')
                result["order_id"] = processing_result.get('order_id')
            else:
                result["errors"].append(f"Processing failed: {processing_result.get('message', 'Unknown error') if processing_result else 'No response'}")
                
        except Exception as e:
            result["errors"].append(f"Exception: {str(e)}")
        
        finally:
            result["processing_time"] = time.time() - start_time
            
            # Thread-safe result storage
            with self.lock:
                self.results.append(result)
                status = "✅" if result["success"] else "❌"
                self.logger.info(f"Worker {worker_id} ({instrument}): {status} {result.get('action', 'UNKNOWN')} ({result['processing_time']:.3f}s)")
        
        return result
    
    def test_concurrent_multi_instrument(self, signals_per_instrument: int = 5):
        """Test concurrent signals across multiple instruments"""
        self.logger.info(f"🚀 Testing {signals_per_instrument} concurrent signals per instrument (NIFTY & SENSEX)...")
        
        # Define test signals for different instruments
        nifty_signals = [
            "===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:00+05:30",
            "===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25100 PUT$75.5$70.8$2025-09-12 14:34:00+05:30",
            "===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25200 CALL$145.2$138.5$2025-09-12 14:35:00+05:30",
            "===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 24900 PUT$85.3$80.1$2025-09-12 14:36:00+05:30",
            "===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25300 CALL$125.7$119.8$2025-09-12 14:37:00+05:30"
        ]
        
        sensex_signals = [
            "===Algo_Trading===$TRADE$BUY$SENSEX 18 SEP 82000 PUT$152.9$146.3$2025-09-12 14:33:00+05:30",
            "===Algo_Trading===$TRADE$BUY$SENSEX 18 SEP 81500 CALL$175.4$168.2$2025-09-12 14:34:00+05:30",
            "===Algo_Trading===$TRADE$BUY$SENSEX 18 SEP 82500 PUT$135.6$129.8$2025-09-12 14:35:00+05:30",
            "===Algo_Trading===$TRADE$BUY$SENSEX 18 SEP 81000 CALL$195.3$187.1$2025-09-12 14:36:00+05:30",
            "===Algo_Trading===$TRADE$BUY$SENSEX 18 SEP 83000 PUT$115.8$110.9$2025-09-12 14:37:00+05:30"
        ]
        
        # Prepare signals and threads
        threads = []
        worker_id = 0
        start_time = time.time()
        
        # Launch NIFTY signals
        for i in range(min(signals_per_instrument, len(nifty_signals))):
            thread = threading.Thread(
                target=self.process_signal_async,
                args=(worker_id, nifty_signals[i], "NIFTY")
            )
            threads.append(thread)
            thread.start()
            worker_id += 1
        
        # Launch SENSEX signals
        for i in range(min(signals_per_instrument, len(sensex_signals))):
            thread = threading.Thread(
                target=self.process_signal_async,
                args=(worker_id, sensex_signals[i], "SENSEX")
            )
            threads.append(thread)
            thread.start()
            worker_id += 1
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join(timeout=60)
        
        total_time = time.time() - start_time
        
        # Analyze results by instrument
        nifty_results = [r for r in self.results if r["instrument"] == "NIFTY"]
        sensex_results = [r for r in self.results if r["instrument"] == "SENSEX"]
        
        nifty_success = len([r for r in nifty_results if r["success"]])
        sensex_success = len([r for r in sensex_results if r["success"]])
        total_success = nifty_success + sensex_success
        total_signals = len(self.results)
        
        return {
            "test_type": "multi_instrument_concurrent",
            "total_signals": total_signals,
            "total_successful": total_success,
            "total_failed": total_signals - total_success,
            "total_time": total_time,
            "nifty_stats": {
                "total": len(nifty_results),
                "successful": nifty_success,
                "success_rate": (nifty_success / len(nifty_results) * 100) if nifty_results else 0
            },
            "sensex_stats": {
                "total": len(sensex_results),
                "successful": sensex_success,
                "success_rate": (sensex_success / len(sensex_results) * 100) if sensex_results else 0
            },
            "overall_success_rate": (total_success / total_signals * 100) if total_signals > 0 else 0,
            "average_processing_time": sum(r["processing_time"] for r in self.results) / len(self.results) if self.results else 0,
            "orders_placed": [r["order_id"] for r in self.results if r["order_id"]],
            "errors": [r["errors"] for r in self.results if r["errors"]]
        }

def main():
    """Main execution function"""
    print("🚀 Starting Multi-Instrument Concurrent Test")
    print("=" * 50)
    
    # Create test instance
    test_suite = MultiInstrumentConcurrentTest()
    
    # Run concurrent multi-instrument test
    results = test_suite.test_concurrent_multi_instrument(5)
    
    # Print detailed summary
    print(f"\n📊 MULTI-INSTRUMENT CONCURRENT TEST RESULTS")
    print(f"Total Signals Processed: {results['total_signals']}")
    print(f"Successful: {results['total_successful']}")
    print(f"Failed: {results['total_failed']}")
    print(f"Overall Success Rate: {results['overall_success_rate']:.1f}%")
    print(f"Total Processing Time: {results['total_time']:.2f}s")
    print(f"Average Processing Time: {results['average_processing_time']:.3f}s")
    
    print(f"\n📈 NIFTY Results:")
    print(f"  Signals: {results['nifty_stats']['total']}")
    print(f"  Successful: {results['nifty_stats']['successful']}")
    print(f"  Success Rate: {results['nifty_stats']['success_rate']:.1f}%")
    
    print(f"\n📈 SENSEX Results:")
    print(f"  Signals: {results['sensex_stats']['total']}")
    print(f"  Successful: {results['sensex_stats']['successful']}")
    print(f"  Success Rate: {results['sensex_stats']['success_rate']:.1f}%")
    
    if results['orders_placed']:
        print(f"\n📋 Orders Placed: {len(results['orders_placed'])}")
        for i, order_id in enumerate(results['orders_placed'][:5], 1):
            print(f"  {i}. {order_id}")
        if len(results['orders_placed']) > 5:
            print(f"  ... and {len(results['orders_placed']) - 5} more")
    
    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f'tests/multi_instrument_results_{timestamp}.json'
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n📄 Detailed results saved to: {results_file}")
    print("🏁 Multi-instrument concurrent test completed!")

if __name__ == "__main__":
    main()
