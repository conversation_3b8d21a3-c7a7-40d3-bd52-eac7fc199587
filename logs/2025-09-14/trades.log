2025-09-14 11:05:15 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$155.4$151.2$2025-09-12 10:53:00+05:30
2025-09-14 11:05:15 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 10, 53, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=155.4, stop_loss=151.2, entry_price=155.4, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 11:05:15 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 11:05:16 - trade - ERROR - API signal processing failed: {'status': 'error', 'message': 'Unable to fetch market data'}
2025-09-14 11:05:44 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$CLOSE$NIFTY 16 SEP 25000 CALL$154.65$2025-09-12 10:54:00+05:30$stop_loss_exit$154.65
2025-09-14 11:05:44 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='CLOSE', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 10, 54, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=154.65, stop_loss=None, entry_price=None, exit_price=154.65, current_price=None, exit_reason='stop_loss_exit', signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 11:05:44 - trade - INFO - Dataframe updated: Trade completed and logged for NIFTY 16 SEP 25000 CALL
2025-09-14 11:05:44 - trade - ERROR - API signal processing failed: {'status': 'error', 'message': 'Unable to fetch market data'}
2025-09-14 11:36:31 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$155.4$151.2$2025-09-12 10:53:00+05:30
2025-09-14 11:36:31 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 10, 53, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=155.4, stop_loss=151.2, entry_price=155.4, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 11:36:31 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 11:36:32 - trade - ERROR - API signal processing failed: {'status': 'error', 'message': "'NoneType' object has no attribute 'get'"}
2025-09-14 11:36:37 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$CLOSE$NIFTY 16 SEP 25000 CALL$154.65$2025-09-12 10:54:00+05:30$stop_loss_exit$154.65
2025-09-14 11:36:37 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='CLOSE', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 10, 54, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=154.65, stop_loss=None, entry_price=None, exit_price=154.65, current_price=None, exit_reason='stop_loss_exit', signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 11:36:37 - trade - INFO - Dataframe updated: Trade completed and logged for NIFTY 16 SEP 25000 CALL
2025-09-14 11:36:37 - trade - ERROR - API signal processing failed: {'status': 'error', 'message': 'Invalid action CLOSE'}
2025-09-14 11:44:35 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$155.4$151.2$2025-09-12 10:53:00+05:30
2025-09-14 11:44:35 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 10, 53, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=155.4, stop_loss=151.2, entry_price=155.4, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 11:44:36 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 11:44:36 - trade - ERROR - API signal processing failed: {'status': 'error', 'message': "Trade execution error: 'str' object has no attribute 'get'"}
2025-09-14 11:45:07 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$CLOSE$NIFTY 16 SEP 25000 CALL$154.65$2025-09-12 10:54:00+05:30$stop_loss_exit$154.65
2025-09-14 11:45:07 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='CLOSE', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 10, 54, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=154.65, stop_loss=None, entry_price=None, exit_price=154.65, current_price=None, exit_reason='stop_loss_exit', signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 11:45:07 - trade - INFO - Dataframe updated: Trade completed and logged for NIFTY 16 SEP 25000 CALL
2025-09-14 11:45:08 - trade - ERROR - API signal processing failed: {'status': 'error', 'message': 'No active position found for NIFTY'}
2025-09-14 11:47:27 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$155.4$151.2$2025-09-12 10:53:00+05:30
2025-09-14 11:47:27 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 10, 53, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=155.4, stop_loss=151.2, entry_price=155.4, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 11:47:28 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 11:47:29 - trade - ERROR - API signal processing failed: {'status': 'error', 'message': "Trade execution error: 'str' object has no attribute 'get'"}
2025-09-14 11:47:37 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$CLOSE$NIFTY 16 SEP 25000 CALL$154.65$2025-09-12 10:54:00+05:30$stop_loss_exit$154.65
2025-09-14 11:47:37 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='CLOSE', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 10, 54, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=154.65, stop_loss=None, entry_price=None, exit_price=154.65, current_price=None, exit_reason='stop_loss_exit', signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 11:47:38 - trade - INFO - Dataframe updated: Trade completed and logged for NIFTY 16 SEP 25000 CALL
2025-09-14 11:47:38 - trade - ERROR - API signal processing failed: {'status': 'error', 'message': 'No active position found for NIFTY'}
2025-09-14 11:50:16 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$155.4$151.2$2025-09-12 10:53:00+05:30
2025-09-14 11:50:16 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 10, 53, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=155.4, stop_loss=151.2, entry_price=155.4, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 11:50:17 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 11:50:18 - trade - ERROR - API signal processing failed: {'status': 'error', 'message': 'Unexpected response format: 09142e140e87AO'}
2025-09-14 11:50:21 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$CLOSE$NIFTY 16 SEP 25000 CALL$154.65$2025-09-12 10:54:00+05:30$stop_loss_exit$154.65
2025-09-14 11:50:21 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='CLOSE', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 10, 54, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=154.65, stop_loss=None, entry_price=None, exit_price=154.65, current_price=None, exit_reason='stop_loss_exit', signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 11:50:22 - trade - INFO - Dataframe updated: Trade completed and logged for NIFTY 16 SEP 25000 CALL
2025-09-14 11:50:22 - trade - ERROR - API signal processing failed: {'status': 'error', 'message': 'No active position found for NIFTY'}
2025-09-14 11:55:17 - trade - INFO - Processing message: ...$154.65$2025-09-12 10:54:00+05:30$stop_loss_exit$154.65


===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$155.4$151.2$2025-09-12 10:53:00+05:30
2025-09-14 11:55:41 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$155.4$151.2$2025-09-12 10:53:00+05:30
2025-09-14 11:55:41 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 10, 53, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=155.4, stop_loss=151.2, entry_price=155.4, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 11:55:42 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 11:55:42 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '0914957cae62AO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 11:56:21 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$CLOSE$NIFTY 16 SEP 25000 CALL$154.65$2025-09-12 10:54:00+05:30$stop_loss_exit$154.65
2025-09-14 11:56:21 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='CLOSE', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 10, 54, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=154.65, stop_loss=None, entry_price=None, exit_price=154.65, current_price=None, exit_reason='stop_loss_exit', signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 11:56:22 - trade - INFO - Dataframe updated: Trade completed and logged for NIFTY 16 SEP 25000 CALL
2025-09-14 11:56:22 - trade - ERROR - API signal processing failed: {'status': 'error', 'message': "'str' object has no attribute 'get'"}
2025-09-14 11:58:10 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$155.4$151.2$2025-09-12 10:53:00+05:30
2025-09-14 11:58:10 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 10, 53, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=155.4, stop_loss=151.2, entry_price=155.4, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 11:58:11 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 11:58:11 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '09146cb3ce51AO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 11:58:16 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$CLOSE$NIFTY 16 SEP 25000 CALL$154.65$2025-09-12 10:54:00+05:30$stop_loss_exit$154.65
2025-09-14 11:58:16 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='CLOSE', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 10, 54, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=154.65, stop_loss=None, entry_price=None, exit_price=154.65, current_price=None, exit_reason='stop_loss_exit', signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 11:58:17 - trade - INFO - Dataframe updated: Trade completed and logged for NIFTY 16 SEP 25000 CALL
2025-09-14 11:58:17 - trade - ERROR - API signal processing failed: {'status': 'error', 'message': "'str' object has no attribute 'get'"}
2025-09-14 12:04:23 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$155.4$151.2$2025-09-12 10:53:00+05:30
2025-09-14 12:04:23 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 10, 53, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=155.4, stop_loss=151.2, entry_price=155.4, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:04:24 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 12:04:25 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '0914f875cb6aAO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:04:28 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$CLOSE$NIFTY 16 SEP 25000 CALL$154.65$2025-09-12 10:54:00+05:30$stop_loss_exit$154.65
2025-09-14 12:04:28 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='CLOSE', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 10, 54, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=154.65, stop_loss=None, entry_price=None, exit_price=154.65, current_price=None, exit_reason='stop_loss_exit', signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:04:29 - trade - INFO - Dataframe updated: Trade completed and logged for NIFTY 16 SEP 25000 CALL
2025-09-14 12:04:30 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Position exited successfully: SELL 75 units', 'order_id': '09146df0348eAO'}
