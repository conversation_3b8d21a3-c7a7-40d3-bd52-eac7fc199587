2025-09-14 12:25:33 - trade - ERROR - Error parsing option chain 'Instrument_name        trade_entry_time          trade_exit_time            Trade_entry_price Trade_exit_price profit_loss
NIFTY 16 SEP 25000 PUT 2025-09-12 14:48:00+05:30 2025-09-12 15:00:00+05:30 30.6                30.33           -80.25': could not convert string to float: 'Trade_entry_price'
2025-09-14 12:30:49 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$SENSEX 18 SEP 82000 PUT$171.24$162.68$2025-09-12 13:00:32+05:30
2025-09-14 12:30:49 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$SENSEX 18 SEP 81500 PUT$140.84$133.8$2025-09-12 10:11:07+05:30
2025-09-14 12:30:49 - trade - INFO - Parsed signal: TradeSignal(symbol='SENSEX', action='BUY', strike=82000.0, option_type='PE', timestamp=datetime.datetime(2025, 9, 12, 13, 0, 32, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=171.24, stop_loss=162.68, entry_price=171.24, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='18 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:49 - trade - INFO - Parsed signal: TradeSignal(symbol='SENSEX', action='BUY', strike=81500.0, option_type='PE', timestamp=datetime.datetime(2025, 9, 12, 10, 11, 7, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=140.84, stop_loss=133.8, entry_price=140.84, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='18 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:49 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25200 CALL$170.82$162.28$2025-09-12 10:24:50+05:30
2025-09-14 12:30:49 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25200.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 10, 24, 50, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=170.82, stop_loss=162.28, entry_price=170.82, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:49 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$SENSEX 18 SEP 81500 PUT$172.22$163.61$2025-09-12 13:35:15+05:30
2025-09-14 12:30:49 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25200 PUT$77.89$74.0$2025-09-12 12:32:44+05:30
2025-09-14 12:30:49 - trade - INFO - Parsed signal: TradeSignal(symbol='SENSEX', action='BUY', strike=81500.0, option_type='PE', timestamp=datetime.datetime(2025, 9, 12, 13, 35, 15, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=172.22, stop_loss=163.61, entry_price=172.22, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='18 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:49 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25200.0, option_type='PE', timestamp=datetime.datetime(2025, 9, 12, 12, 32, 44, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=77.89, stop_loss=74.0, entry_price=77.89, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:49 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25200 CALL$94.28$89.57$2025-09-12 11:34:04+05:30
2025-09-14 12:30:49 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25200.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 11, 34, 4, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=94.28, stop_loss=89.57, entry_price=94.28, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:49 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$SENSEX 18 SEP 81000 PUT$161.0$152.95$2025-09-12 12:29:14+05:30
2025-09-14 12:30:49 - trade - INFO - Parsed signal: TradeSignal(symbol='SENSEX', action='BUY', strike=81000.0, option_type='PE', timestamp=datetime.datetime(2025, 9, 12, 12, 29, 14, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=161.0, stop_loss=152.95, entry_price=161.0, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='18 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:49 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 PUT$69.19$65.73$2025-09-12 13:16:17+05:30
2025-09-14 12:30:49 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='PE', timestamp=datetime.datetime(2025, 9, 12, 13, 16, 17, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=69.19, stop_loss=65.73, entry_price=69.19, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:49 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25200 PUT$66.5$63.17$2025-09-12 12:45:46+05:30
2025-09-14 12:30:49 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25200.0, option_type='PE', timestamp=datetime.datetime(2025, 9, 12, 12, 45, 46, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=66.5, stop_loss=63.17, entry_price=66.5, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:49 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$SENSEX 18 SEP 82000 PUT$51.28$48.72$2025-09-12 14:40:34+05:30
2025-09-14 12:30:49 - trade - INFO - Parsed signal: TradeSignal(symbol='SENSEX', action='BUY', strike=82000.0, option_type='PE', timestamp=datetime.datetime(2025, 9, 12, 14, 40, 34, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=51.28, stop_loss=48.72, entry_price=51.28, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='18 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:51 - trade - INFO - Dataframe updated: Trade entry created for SENSEX 18 SEP 81500 PUT
2025-09-14 12:30:51 - trade - INFO - Dataframe updated: Trade entry created for SENSEX 18 SEP 82000 PUT
2025-09-14 12:30:51 - trade - INFO - Dataframe updated: Trade entry created for SENSEX 18 SEP 81500 PUT
2025-09-14 12:30:51 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25200 CALL
2025-09-14 12:30:51 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25200 PUT
2025-09-14 12:30:51 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 PUT
2025-09-14 12:30:51 - trade - INFO - Dataframe updated: Trade entry created for SENSEX 18 SEP 82000 PUT
2025-09-14 12:30:51 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25200 PUT
2025-09-14 12:30:51 - trade - INFO - Dataframe updated: Trade entry created for SENSEX 18 SEP 81000 PUT
2025-09-14 12:30:52 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 20 units', 'order_id': '09140d695013AO', 'price': 346.5, 'quantity': 20, 'lots': 1}
2025-09-14 12:30:52 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 20 units', 'order_id': '09147716a69eAO', 'price': 158.45, 'quantity': 20, 'lots': 1}
2025-09-14 12:30:52 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '0914c2278062AO', 'price': 109.95, 'quantity': 75, 'lots': 1}
2025-09-14 12:30:52 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '0914880880ecAO', 'price': 109.95, 'quantity': 75, 'lots': 1}
2025-09-14 12:30:52 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '09148aa812a2AO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:30:52 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 20 units', 'order_id': '091490fccadbAO', 'price': 158.45, 'quantity': 20, 'lots': 1}
2025-09-14 12:30:52 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '091452913c9dAO', 'price': 109.95, 'quantity': 75, 'lots': 1}
2025-09-14 12:30:52 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 20 units', 'order_id': '0914eada5ef4AO', 'price': 346.5, 'quantity': 20, 'lots': 1}
2025-09-14 12:30:52 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 20 units', 'order_id': '0914ca4c8261AO', 'price': 1090.85, 'quantity': 20, 'lots': 1}
2025-09-14 12:30:53 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25200 CALL
2025-09-14 12:30:53 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '0914dd17cc0eAO', 'price': 109.95, 'quantity': 75, 'lots': 1}
2025-09-14 12:30:55 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$CLOSE$SENSEX 18 SEP 81000 CALL$136.83$2025-09-12 15:37:13+05:30$profit_booking$136.83
2025-09-14 12:30:55 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$CLOSE$NIFTY 16 SEP 24900 PUT$157.91$2025-09-12 14:07:15+05:30$profit_booking$157.91
2025-09-14 12:30:55 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$CLOSE$NIFTY 16 SEP 25100 CALL$52.4$2025-09-12 09:16:42+05:30$profit_booking$52.4
2025-09-14 12:30:55 - trade - INFO - Parsed signal: TradeSignal(symbol='SENSEX', action='CLOSE', strike=81000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 15, 37, 13, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=136.83, stop_loss=None, entry_price=None, exit_price=136.83, current_price=None, exit_reason='profit_booking', signal_type='TRADE', expiry_date='18 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:55 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25200 CALL$83.04$78.89$2025-09-12 10:52:25+05:30
2025-09-14 12:30:55 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$SENSEX 18 SEP 83000 CALL$136.67$129.84$2025-09-12 09:17:06+05:30
2025-09-14 12:30:55 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='CLOSE', strike=24900.0, option_type='PE', timestamp=datetime.datetime(2025, 9, 12, 14, 7, 15, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=157.91, stop_loss=None, entry_price=None, exit_price=157.91, current_price=None, exit_reason='profit_booking', signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:55 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$CLOSE$SENSEX 18 SEP 81500 CALL$151.95$2025-09-12 15:14:10+05:30$profit_booking$151.95
2025-09-14 12:30:55 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 24900 CALL$102.62$97.49$2025-09-12 15:00:03+05:30
2025-09-14 12:30:55 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$CLOSE$NIFTY 16 SEP 24800 CALL$117.38$2025-09-12 13:33:53+05:30$profit_booking$117.38
2025-09-14 12:30:55 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='CLOSE', strike=25100.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 9, 16, 42, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=52.4, stop_loss=None, entry_price=None, exit_price=52.4, current_price=None, exit_reason='profit_booking', signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:55 - trade - INFO - Processing message: ===Algo_Trading===$INTIMATION$Continue to hold trade for $SENSEX 16 SEP 83000 CALL at current price: 164.47
2025-09-14 12:30:55 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$CLOSE$SENSEX 18 SEP 82500 PUT$150.59$2025-09-12 11:42:26+05:30$profit_booking$150.59
2025-09-14 12:30:55 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$CLOSE$SENSEX 18 SEP 81500 CALL$119.59$2025-09-12 10:44:34+05:30$profit_booking$119.59
2025-09-14 12:30:55 - trade - INFO - Processing message: ===Algo_Trading===$Update$STOP LOSS to$68.94$for option$SENSEX 16 SEP 83000 CALL
2025-09-14 12:30:55 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$CLOSE$SENSEX 18 SEP 83000 PUT$186.5$2025-09-12 10:28:14+05:30$profit_booking$186.5
2025-09-14 12:30:55 - trade - INFO - Processing message: ===Algo_Trading===$INTIMATION$Continue to hold trade for $SENSEX 16 SEP 82000 CALL at current price: 106.42
2025-09-14 12:30:55 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 PUT$114.95$109.2$2025-09-12 12:50:58+05:30
2025-09-14 12:30:55 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25200.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 10, 52, 25, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=83.04, stop_loss=78.89, entry_price=83.04, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:55 - trade - INFO - Parsed signal: TradeSignal(symbol='SENSEX', action='BUY', strike=83000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 9, 17, 6, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=136.67, stop_loss=129.84, entry_price=136.67, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='18 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:55 - trade - INFO - Parsed signal: TradeSignal(symbol='SENSEX', action='CLOSE', strike=81500.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 15, 14, 10, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=151.95, stop_loss=None, entry_price=None, exit_price=151.95, current_price=None, exit_reason='profit_booking', signal_type='TRADE', expiry_date='18 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:55 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=24900.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 15, 0, 3, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=102.62, stop_loss=97.49, entry_price=102.62, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:55 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='CLOSE', strike=24800.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 13, 33, 53, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=117.38, stop_loss=None, entry_price=None, exit_price=117.38, current_price=None, exit_reason='profit_booking', signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:55 - trade - INFO - Parsed signal: TradeSignal(symbol='SENSEX', action='HOLD', strike=83000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 14, 12, 30, 48, 353070), quantity=1, price=164.47, stop_loss=None, entry_price=None, exit_price=None, current_price=164.47, exit_reason=None, signal_type='INTIMATION', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:55 - trade - ERROR - Dataframe update failed: No active trade found for SENSEX 18 SEP 81000 CALL to close
2025-09-14 12:30:55 - trade - INFO - Parsed signal: TradeSignal(symbol='SENSEX', action='CLOSE', strike=82500.0, option_type='PE', timestamp=datetime.datetime(2025, 9, 12, 11, 42, 26, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=150.59, stop_loss=None, entry_price=None, exit_price=150.59, current_price=None, exit_reason='profit_booking', signal_type='TRADE', expiry_date='18 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:55 - trade - INFO - Parsed signal: TradeSignal(symbol='SENSEX', action='CLOSE', strike=81500.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 10, 44, 34, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=119.59, stop_loss=None, entry_price=None, exit_price=119.59, current_price=None, exit_reason='profit_booking', signal_type='TRADE', expiry_date='18 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:55 - trade - INFO - Parsed signal: TradeSignal(symbol='SENSEX', action='UPDATE', strike=83000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 14, 12, 30, 48, 353070), quantity=1, price=None, stop_loss=None, entry_price=None, exit_price=None, current_price=None, exit_reason=None, signal_type='UPDATE', expiry_date='16 SEP', update_type='STOP LOSS', new_stop_loss=68.94)
2025-09-14 12:30:55 - trade - INFO - Parsed signal: TradeSignal(symbol='SENSEX', action='CLOSE', strike=83000.0, option_type='PE', timestamp=datetime.datetime(2025, 9, 12, 10, 28, 14, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=186.5, stop_loss=None, entry_price=None, exit_price=186.5, current_price=None, exit_reason='profit_booking', signal_type='TRADE', expiry_date='18 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:55 - trade - INFO - Parsed signal: TradeSignal(symbol='SENSEX', action='HOLD', strike=82000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 14, 12, 30, 48, 353070), quantity=1, price=106.42, stop_loss=None, entry_price=None, exit_price=None, current_price=106.42, exit_reason=None, signal_type='INTIMATION', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:55 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='PE', timestamp=datetime.datetime(2025, 9, 12, 12, 50, 58, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=114.95, stop_loss=109.2, entry_price=114.95, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:55 - trade - ERROR - Dataframe update failed: No active trade found for NIFTY 16 SEP 24900 PUT to close
2025-09-14 12:30:55 - trade - ERROR - Dataframe update failed: No active trade found for NIFTY 16 SEP 25100 CALL to close
2025-09-14 12:30:55 - trade - ERROR - Dataframe update failed: No active trade found for SENSEX 18 SEP 83000 PUT to close
2025-09-14 12:30:55 - trade - ERROR - Dataframe update failed: No active trade found for SENSEX 18 SEP 81500 CALL to close
2025-09-14 12:30:56 - trade - ERROR - Dataframe update failed: No active trade found for NIFTY 16 SEP 24800 CALL to close
2025-09-14 12:30:56 - trade - ERROR - Dataframe update failed: Intimation received for SENSEX 16 SEP 83000 CALL but no active trade found
2025-09-14 12:30:56 - trade - ERROR - Dataframe update failed: No active trade found for SENSEX 18 SEP 82500 PUT to close
2025-09-14 12:30:56 - trade - ERROR - Dataframe update failed: No active trade found for SENSEX 18 SEP 81500 CALL to close
2025-09-14 12:30:56 - trade - ERROR - Dataframe update failed: Update received for SENSEX 16 SEP 83000 CALL but no active trade found
2025-09-14 12:30:56 - trade - ERROR - Dataframe update failed: Intimation received for SENSEX 16 SEP 82000 CALL but no active trade found
2025-09-14 12:30:56 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Position exited successfully: SELL 20 units', 'order_id': '091461a17f3dAO'}
2025-09-14 12:30:56 - trade - ERROR - API signal processing failed: {'status': 'error', 'message': 'No active position found for SENSEX'}
2025-09-14 12:30:56 - trade - ERROR - API signal processing failed: {'status': 'error', 'message': 'No active position found for SENSEX'}
2025-09-14 12:30:56 - trade - ERROR - API signal processing failed: {'status': 'error', 'message': 'No active position found for SENSEX'}
2025-09-14 12:30:56 - trade - ERROR - API signal processing failed: {'status': 'error', 'message': "'SENSEX'"}
2025-09-14 12:30:56 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Position exited successfully: SELL 75 units', 'order_id': '091439023b76AO'}
2025-09-14 12:30:56 - trade - ERROR - API signal processing failed: {'status': 'error', 'message': "'NIFTY'"}
2025-09-14 12:30:56 - trade - ERROR - API signal processing failed: {'status': 'error', 'message': "'NIFTY'"}
2025-09-14 12:30:57 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 24900 CALL
2025-09-14 12:30:57 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25200 CALL
2025-09-14 12:30:57 - trade - INFO - Dataframe updated: Trade entry created for SENSEX 18 SEP 83000 CALL
2025-09-14 12:30:57 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 PUT
2025-09-14 12:30:57 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '0914136198fdAO', 'price': 250.7, 'quantity': 75, 'lots': 1}
2025-09-14 12:30:57 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '0914cb4dcb57AO', 'price': 109.95, 'quantity': 75, 'lots': 1}
2025-09-14 12:30:57 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '091428d784e9AO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:30:57 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 20 units', 'order_id': '09140f910501AO', 'price': 53.5, 'quantity': 20, 'lots': 1}
2025-09-14 12:30:59 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:00+05:30
2025-09-14 12:30:59 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:00+05:30
2025-09-14 12:30:59 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:00+05:30
2025-09-14 12:30:59 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:00+05:30
2025-09-14 12:30:59 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=165.8, stop_loss=160.2, entry_price=165.8, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:59 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:00+05:30
2025-09-14 12:30:59 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:00+05:30
2025-09-14 12:30:59 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:00+05:30
2025-09-14 12:30:59 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=165.8, stop_loss=160.2, entry_price=165.8, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:59 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:00+05:30
2025-09-14 12:30:59 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=165.8, stop_loss=160.2, entry_price=165.8, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:59 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:00+05:30
2025-09-14 12:30:59 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:00+05:30
2025-09-14 12:30:59 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=165.8, stop_loss=160.2, entry_price=165.8, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:59 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:00+05:30
2025-09-14 12:30:59 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:00+05:30
2025-09-14 12:30:59 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:00+05:30
2025-09-14 12:30:59 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=165.8, stop_loss=160.2, entry_price=165.8, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:59 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:00+05:30
2025-09-14 12:30:59 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:00+05:30
2025-09-14 12:30:59 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=165.8, stop_loss=160.2, entry_price=165.8, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:59 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:00+05:30
2025-09-14 12:30:59 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=165.8, stop_loss=160.2, entry_price=165.8, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:59 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:00+05:30
2025-09-14 12:30:59 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=165.8, stop_loss=160.2, entry_price=165.8, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:59 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:00+05:30
2025-09-14 12:30:59 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:00+05:30
2025-09-14 12:30:59 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:00+05:30
2025-09-14 12:30:59 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=165.8, stop_loss=160.2, entry_price=165.8, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:59 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=165.8, stop_loss=160.2, entry_price=165.8, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:59 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=165.8, stop_loss=160.2, entry_price=165.8, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:59 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=165.8, stop_loss=160.2, entry_price=165.8, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:59 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=165.8, stop_loss=160.2, entry_price=165.8, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:59 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=165.8, stop_loss=160.2, entry_price=165.8, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:59 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=165.8, stop_loss=160.2, entry_price=165.8, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:59 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=165.8, stop_loss=160.2, entry_price=165.8, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:59 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=165.8, stop_loss=160.2, entry_price=165.8, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:59 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=165.8, stop_loss=160.2, entry_price=165.8, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:59 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=165.8, stop_loss=160.2, entry_price=165.8, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:30:59 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=165.8, stop_loss=160.2, entry_price=165.8, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:31:03 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 12:31:03 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 12:31:03 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 12:31:03 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 12:31:03 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 12:31:03 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 12:31:03 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 12:31:03 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 12:31:04 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 12:31:04 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 12:31:04 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 12:31:04 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 12:31:04 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 12:31:04 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 12:31:04 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 12:31:04 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 12:31:04 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 12:31:04 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 12:31:04 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 12:31:04 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '0914b5425f06AO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:31:04 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '0914e2ee3c7dAO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:31:05 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '0914929da5b4AO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:31:05 - trade - ERROR - API signal processing failed: {'status': 'warning', 'message': 'Unable to fetch live market data for NIFTY. Signal logged only.'}
2025-09-14 12:31:05 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '09146fff62a2AO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:31:05 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '091466cf67b3AO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:31:05 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '09144fe99453AO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:31:05 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '0914ef4a163fAO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:31:05 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '09149b70dacfAO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:31:05 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '091430dd6cfaAO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:31:05 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '091422714471AO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:31:05 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '0914601d2a28AO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:31:05 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '09140c66fbbdAO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:31:05 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '09147b1f68e6AO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:31:05 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '0914f4f78286AO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:31:05 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '0914f7fcf274AO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:31:05 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '09140892f10cAO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:31:05 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '09148ec1feb3AO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:31:05 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '0914a1f36114AO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:31:07 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 12:31:07 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '0914fc286e6eAO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:32:37 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25100 PUT$75.5$70.8$2025-09-12 14:34:00+05:30
2025-09-14 12:32:37 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:00+05:30
2025-09-14 12:32:37 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25200 CALL$145.2$138.5$2025-09-12 14:35:00+05:30
2025-09-14 12:32:37 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25100.0, option_type='PE', timestamp=datetime.datetime(2025, 9, 12, 14, 34, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=75.5, stop_loss=70.8, entry_price=75.5, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:32:37 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 24900 PUT$85.3$80.1$2025-09-12 14:36:00+05:30
2025-09-14 12:32:37 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=165.8, stop_loss=160.2, entry_price=165.8, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:32:37 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25200.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 35, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=145.2, stop_loss=138.5, entry_price=145.2, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:32:37 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$SENSEX 18 SEP 81500 CALL$175.4$168.2$2025-09-12 14:34:00+05:30
2025-09-14 12:32:37 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$SENSEX 18 SEP 82000 PUT$152.9$146.3$2025-09-12 14:33:00+05:30
2025-09-14 12:32:37 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25300 CALL$125.7$119.8$2025-09-12 14:37:00+05:30
2025-09-14 12:32:37 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$SENSEX 18 SEP 82500 PUT$135.6$129.8$2025-09-12 14:35:00+05:30
2025-09-14 12:32:37 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$SENSEX 18 SEP 81000 CALL$195.3$187.1$2025-09-12 14:36:00+05:30
2025-09-14 12:32:37 - trade - INFO - Processing message: ===Algo_Trading===$TRADE$BUY$SENSEX 18 SEP 83000 PUT$115.8$110.9$2025-09-12 14:37:00+05:30
2025-09-14 12:32:37 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=24900.0, option_type='PE', timestamp=datetime.datetime(2025, 9, 12, 14, 36, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=85.3, stop_loss=80.1, entry_price=85.3, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:32:37 - trade - INFO - Parsed signal: TradeSignal(symbol='SENSEX', action='BUY', strike=81500.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 34, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=175.4, stop_loss=168.2, entry_price=175.4, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='18 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:32:37 - trade - INFO - Parsed signal: TradeSignal(symbol='SENSEX', action='BUY', strike=82000.0, option_type='PE', timestamp=datetime.datetime(2025, 9, 12, 14, 33, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=152.9, stop_loss=146.3, entry_price=152.9, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='18 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:32:37 - trade - INFO - Parsed signal: TradeSignal(symbol='NIFTY', action='BUY', strike=25300.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 37, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=125.7, stop_loss=119.8, entry_price=125.7, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='16 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:32:37 - trade - INFO - Parsed signal: TradeSignal(symbol='SENSEX', action='BUY', strike=82500.0, option_type='PE', timestamp=datetime.datetime(2025, 9, 12, 14, 35, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=135.6, stop_loss=129.8, entry_price=135.6, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='18 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:32:37 - trade - INFO - Parsed signal: TradeSignal(symbol='SENSEX', action='BUY', strike=81000.0, option_type='CE', timestamp=datetime.datetime(2025, 9, 12, 14, 36, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=195.3, stop_loss=187.1, entry_price=195.3, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='18 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:32:37 - trade - INFO - Parsed signal: TradeSignal(symbol='SENSEX', action='BUY', strike=83000.0, option_type='PE', timestamp=datetime.datetime(2025, 9, 12, 14, 37, tzinfo=datetime.timezone(datetime.timedelta(seconds=19800))), quantity=1, price=115.8, stop_loss=110.9, entry_price=115.8, exit_price=None, current_price=None, exit_reason=None, signal_type='TRADE', expiry_date='18 SEP', update_type=None, new_stop_loss=None)
2025-09-14 12:32:39 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25000 CALL
2025-09-14 12:32:39 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25100 PUT
2025-09-14 12:32:39 - trade - INFO - Dataframe updated: Trade entry created for SENSEX 18 SEP 82000 PUT
2025-09-14 12:32:39 - trade - INFO - Dataframe updated: Trade entry created for SENSEX 18 SEP 82500 PUT
2025-09-14 12:32:39 - trade - INFO - Dataframe updated: Trade entry created for SENSEX 18 SEP 83000 PUT
2025-09-14 12:32:39 - trade - INFO - Dataframe updated: Trade entry created for SENSEX 18 SEP 81000 CALL
2025-09-14 12:32:39 - trade - INFO - Dataframe updated: Trade entry created for SENSEX 18 SEP 81500 CALL
2025-09-14 12:32:39 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 24900 PUT
2025-09-14 12:32:39 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25200 CALL
2025-09-14 12:32:39 - trade - INFO - Dataframe updated: Trade entry created for NIFTY 16 SEP 25300 CALL
2025-09-14 12:32:40 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '091422fd5dc5AO', 'price': 91.85, 'quantity': 75, 'lots': 1}
2025-09-14 12:32:40 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '0914e96463eeAO', 'price': 163.8, 'quantity': 75, 'lots': 1}
2025-09-14 12:32:40 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 20 units', 'order_id': '0914a30b8b88AO', 'price': 346.5, 'quantity': 20, 'lots': 1}
2025-09-14 12:32:40 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 20 units', 'order_id': '0914e696e2b4AO', 'price': 145.2, 'quantity': 20, 'lots': 1}
2025-09-14 12:32:40 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 20 units', 'order_id': '091458f7967bAO', 'price': 158.45, 'quantity': 20, 'lots': 1}
2025-09-14 12:32:40 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 20 units', 'order_id': '0914cfc48428AO', 'price': 53.5, 'quantity': 20, 'lots': 1}
2025-09-14 12:32:40 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 20 units', 'order_id': '0914b6e062e8AO', 'price': 1090.85, 'quantity': 20, 'lots': 1}
2025-09-14 12:32:40 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '0914d079fc77AO', 'price': 183.55, 'quantity': 75, 'lots': 1}
2025-09-14 12:32:40 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '0914dabb98cbAO', 'price': 250.7, 'quantity': 75, 'lots': 1}
2025-09-14 12:32:40 - trade - INFO - API signal processed successfully: {'status': 'success', 'message': 'Order placed successfully: BUY 75 units', 'order_id': '09148aec66d7AO', 'price': 109.95, 'quantity': 75, 'lots': 1}
