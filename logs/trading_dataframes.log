2025-09-14 12:24:46 - trading_dataframes - INFO - Instrument files updated successfully
2025-09-14 12:24:46 - trading_dataframes - INFO - Reloaded instruments in API handler
2025-09-14 12:24:46 - trading_dataframes - INFO - Trading Dataframe Manager initialized with market data integration
2025-09-14 12:25:24 - trading_dataframes - INFO - Instrument files updated successfully
2025-09-14 12:25:24 - trading_dataframes - INFO - Reloaded instruments in API handler
2025-09-14 12:25:24 - trading_dataframes - INFO - Trading Dataframe Manager initialized with market data integration
2025-09-14 12:25:24 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:25:25 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:25:25 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:25:25 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 PUT at price 30.6
2025-09-14 12:25:26 - trading_dataframes - INFO - Processing signal: INTIMATION - HOLD for NIFTY
2025-09-14 12:25:26 - trading_dataframes - INFO - Intimation received for NIFTY 16 SEP 25000 PUT - continuing to hold
2025-09-14 12:25:27 - trading_dataframes - INFO - Processing signal: UPDATE - UPDATE for NIFTY
2025-09-14 12:25:27 - trading_dataframes - INFO - Update received for NIFTY 16 SEP 25000 PUT: STOP LOSS to 30.25
2025-09-14 12:25:27 - trading_dataframes - INFO - Processing signal: INTIMATION - HOLD for NIFTY
2025-09-14 12:25:27 - trading_dataframes - INFO - Intimation received for NIFTY 16 SEP 25000 PUT - continuing to hold
2025-09-14 12:25:28 - trading_dataframes - INFO - Processing signal: INTIMATION - HOLD for NIFTY
2025-09-14 12:25:28 - trading_dataframes - INFO - Intimation received for NIFTY 16 SEP 25000 PUT - continuing to hold
2025-09-14 12:25:28 - trading_dataframes - INFO - Processing signal: UPDATE - UPDATE for NIFTY
2025-09-14 12:25:28 - trading_dataframes - INFO - Update received for NIFTY 16 SEP 25000 PUT: STOP LOSS to 30.27
2025-09-14 12:25:29 - trading_dataframes - INFO - Processing signal: INTIMATION - HOLD for NIFTY
2025-09-14 12:25:29 - trading_dataframes - INFO - Intimation received for NIFTY 16 SEP 25000 PUT - continuing to hold
2025-09-14 12:25:29 - trading_dataframes - INFO - Processing signal: INTIMATION - HOLD for NIFTY
2025-09-14 12:25:29 - trading_dataframes - INFO - Intimation received for NIFTY 16 SEP 25000 PUT - continuing to hold
2025-09-14 12:25:30 - trading_dataframes - INFO - Processing signal: UPDATE - UPDATE for NIFTY
2025-09-14 12:25:30 - trading_dataframes - INFO - Update received for NIFTY 16 SEP 25000 PUT: STOP LOSS to 30.33
2025-09-14 12:25:30 - trading_dataframes - INFO - Processing signal: INTIMATION - HOLD for NIFTY
2025-09-14 12:25:30 - trading_dataframes - INFO - Intimation received for NIFTY 16 SEP 25000 PUT - continuing to hold
2025-09-14 12:25:31 - trading_dataframes - INFO - Processing signal: INTIMATION - HOLD for NIFTY
2025-09-14 12:25:31 - trading_dataframes - INFO - Intimation received for NIFTY 16 SEP 25000 PUT - continuing to hold
2025-09-14 12:25:31 - trading_dataframes - INFO - Processing signal: TRADE - CLOSE for NIFTY
2025-09-14 12:25:32 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:25:32 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:25:32 - trading_dataframes - INFO - Actual P&L calculated: -0.2700000000000031 (Entry: 30.6, Exit: 30.33)
2025-09-14 12:25:32 - trading_dataframes - INFO - Updated trade exit for NIFTY 16 SEP 25000 PUT at signal price 30.33, actual price 30.33
2025-09-14 12:25:32 - trading_dataframes - INFO - Logged completed trade for NIFTY 16 SEP 25000 PUT to trade_logs/2025-09-14_Trade_Log.csv
2025-09-14 12:25:32 - trading_dataframes - INFO - Cleaned up completed trade for NIFTY 16 SEP 25000 PUT from active memory
2025-09-14 12:29:55 - trading_dataframes - INFO - Instrument files updated successfully
2025-09-14 12:29:55 - trading_dataframes - INFO - Reloaded instruments in API handler
2025-09-14 12:29:55 - trading_dataframes - INFO - Trading Dataframe Manager initialized with market data integration
2025-09-14 12:30:48 - trading_dataframes - INFO - Instrument files updated successfully
2025-09-14 12:30:49 - trading_dataframes - INFO - Reloaded instruments in API handler
2025-09-14 12:30:49 - trading_dataframes - INFO - Trading Dataframe Manager initialized with market data integration
2025-09-14 12:30:49 - trading_dataframes - INFO - Processing signal: TRADE - BUY for SENSEX
2025-09-14 12:30:49 - trading_dataframes - INFO - Processing signal: TRADE - BUY for SENSEX
2025-09-14 12:30:49 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:30:49 - trading_dataframes - INFO - Processing signal: TRADE - BUY for SENSEX
2025-09-14 12:30:49 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:30:49 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:30:49 - trading_dataframes - INFO - Processing signal: TRADE - BUY for SENSEX
2025-09-14 12:30:49 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:30:49 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:30:49 - trading_dataframes - INFO - Processing signal: TRADE - BUY for SENSEX
2025-09-14 12:30:51 - trading_dataframes - WARNING - Could not retrieve market price for SENSEX
2025-09-14 12:30:51 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:30:51 - trading_dataframes - WARNING - Could not retrieve market price for SENSEX
2025-09-14 12:30:51 - trading_dataframes - INFO - Created new trade entry for SENSEX 18 SEP 81500 PUT at price 172.22
2025-09-14 12:30:51 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:30:51 - trading_dataframes - INFO - Created new trade entry for SENSEX 18 SEP 82000 PUT at price 171.24
2025-09-14 12:30:51 - trading_dataframes - WARNING - Could not retrieve market price for SENSEX
2025-09-14 12:30:51 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:30:51 - trading_dataframes - INFO - Created new trade entry for SENSEX 18 SEP 81500 PUT at price 140.84
2025-09-14 12:30:51 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:30:51 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:30:51 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25200 CALL at price 170.82
2025-09-14 12:30:51 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:30:51 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:30:51 - trading_dataframes - WARNING - Could not retrieve market price for SENSEX
2025-09-14 12:30:51 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:30:51 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:30:51 - trading_dataframes - WARNING - Could not retrieve market price for SENSEX
2025-09-14 12:30:51 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:30:51 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:30:51 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25200 PUT at price 77.89
2025-09-14 12:30:51 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:30:51 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:30:51 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 PUT at price 69.19
2025-09-14 12:30:51 - trading_dataframes - INFO - Created new trade entry for SENSEX 18 SEP 82000 PUT at price 51.28
2025-09-14 12:30:51 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25200 PUT at price 66.5
2025-09-14 12:30:51 - trading_dataframes - INFO - Created new trade entry for SENSEX 18 SEP 81000 PUT at price 161.0
2025-09-14 12:30:53 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:30:53 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:30:53 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25200 CALL at price 94.28
2025-09-14 12:30:55 - trading_dataframes - INFO - Processing signal: TRADE - CLOSE for SENSEX
2025-09-14 12:30:55 - trading_dataframes - INFO - Processing signal: TRADE - CLOSE for NIFTY
2025-09-14 12:30:55 - trading_dataframes - ERROR - No active trade found for SENSEX 18 SEP 81000 CALL to close
2025-09-14 12:30:55 - trading_dataframes - ERROR - No active trade found for NIFTY 16 SEP 24900 PUT to close
2025-09-14 12:30:55 - trading_dataframes - INFO - Processing signal: TRADE - CLOSE for NIFTY
2025-09-14 12:30:55 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:30:55 - trading_dataframes - INFO - Processing signal: TRADE - BUY for SENSEX
2025-09-14 12:30:55 - trading_dataframes - INFO - Processing signal: TRADE - CLOSE for SENSEX
2025-09-14 12:30:55 - trading_dataframes - ERROR - No active trade found for NIFTY 16 SEP 25100 CALL to close
2025-09-14 12:30:55 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:30:55 - trading_dataframes - INFO - Processing signal: TRADE - CLOSE for NIFTY
2025-09-14 12:30:55 - trading_dataframes - INFO - Processing signal: INTIMATION - HOLD for SENSEX
2025-09-14 12:30:55 - trading_dataframes - INFO - Processing signal: TRADE - CLOSE for SENSEX
2025-09-14 12:30:55 - trading_dataframes - INFO - Processing signal: TRADE - CLOSE for SENSEX
2025-09-14 12:30:55 - trading_dataframes - INFO - Processing signal: UPDATE - UPDATE for SENSEX
2025-09-14 12:30:55 - trading_dataframes - INFO - Processing signal: TRADE - CLOSE for SENSEX
2025-09-14 12:30:55 - trading_dataframes - INFO - Processing signal: INTIMATION - HOLD for SENSEX
2025-09-14 12:30:55 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:30:55 - trading_dataframes - WARNING - Active trade already exists for NIFTY 16 SEP 25200 CALL. Overwriting with new entry.
2025-09-14 12:30:55 - trading_dataframes - ERROR - No active trade found for SENSEX 18 SEP 81500 CALL to close
2025-09-14 12:30:55 - trading_dataframes - ERROR - No active trade found for NIFTY 16 SEP 24800 CALL to close
2025-09-14 12:30:55 - trading_dataframes - WARNING - Received intimation for SENSEX 16 SEP 83000 CALL but no active trade found
2025-09-14 12:30:55 - trading_dataframes - ERROR - No active trade found for SENSEX 18 SEP 82500 PUT to close
2025-09-14 12:30:55 - trading_dataframes - ERROR - No active trade found for SENSEX 18 SEP 81500 CALL to close
2025-09-14 12:30:55 - trading_dataframes - WARNING - Received update for SENSEX 16 SEP 83000 CALL but no active trade found
2025-09-14 12:30:55 - trading_dataframes - ERROR - No active trade found for SENSEX 18 SEP 83000 PUT to close
2025-09-14 12:30:55 - trading_dataframes - WARNING - Received intimation for SENSEX 16 SEP 82000 CALL but no active trade found
2025-09-14 12:30:56 - trading_dataframes - WARNING - Active trade already exists for NIFTY 16 SEP 25000 PUT. Overwriting with new entry.
2025-09-14 12:30:57 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:30:57 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:30:57 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 24900 CALL at price 102.62
2025-09-14 12:30:57 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:30:57 - trading_dataframes - WARNING - Could not retrieve market price for SENSEX
2025-09-14 12:30:57 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:30:57 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:30:57 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25200 CALL at price 83.04
2025-09-14 12:30:57 - trading_dataframes - INFO - Created new trade entry for SENSEX 18 SEP 83000 CALL at price 136.67
2025-09-14 12:30:57 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:30:57 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:30:57 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 PUT at price 114.95
2025-09-14 12:30:59 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:30:59 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:30:59 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:30:59 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:30:59 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:30:59 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:30:59 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:30:59 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:30:59 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:30:59 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:31:00 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:31:00 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:31:00 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:31:00 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:31:00 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:31:00 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:31:00 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:31:00 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:31:00 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:31:00 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:31:03 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:31:03 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:31:03 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:31:03 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:31:03 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:31:03 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 165.8
2025-09-14 12:31:03 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:31:03 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 165.8
2025-09-14 12:31:03 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 165.8
2025-09-14 12:31:03 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:31:03 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:31:03 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 165.8
2025-09-14 12:31:03 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:31:03 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:31:03 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 165.8
2025-09-14 12:31:03 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:31:03 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:31:03 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:31:03 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 165.8
2025-09-14 12:31:03 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:31:03 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 165.8
2025-09-14 12:31:03 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:31:03 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:31:03 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 165.8
2025-09-14 12:31:04 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:31:04 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:31:04 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 165.8
2025-09-14 12:31:04 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:31:04 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:31:04 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 165.8
2025-09-14 12:31:04 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:31:04 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:31:04 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 165.8
2025-09-14 12:31:04 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:31:04 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:31:04 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 165.8
2025-09-14 12:31:04 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:31:04 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:31:04 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:31:04 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 165.8
2025-09-14 12:31:04 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:31:04 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:31:04 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:31:04 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:31:04 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:31:04 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 165.8
2025-09-14 12:31:04 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:31:04 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:31:04 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:31:04 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:31:04 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:31:04 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:31:04 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 165.8
2025-09-14 12:31:04 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 165.8
2025-09-14 12:31:04 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 165.8
2025-09-14 12:31:04 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 165.8
2025-09-14 12:31:04 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 165.8
2025-09-14 12:31:07 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:31:07 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:31:07 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 165.8
2025-09-14 12:32:37 - trading_dataframes - INFO - Instrument files updated successfully
2025-09-14 12:32:37 - trading_dataframes - INFO - Reloaded instruments in API handler
2025-09-14 12:32:37 - trading_dataframes - INFO - Trading Dataframe Manager initialized with market data integration
2025-09-14 12:32:37 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:32:37 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:32:37 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:32:37 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:32:37 - trading_dataframes - INFO - Processing signal: TRADE - BUY for SENSEX
2025-09-14 12:32:37 - trading_dataframes - INFO - Processing signal: TRADE - BUY for SENSEX
2025-09-14 12:32:37 - trading_dataframes - INFO - Processing signal: TRADE - BUY for NIFTY
2025-09-14 12:32:37 - trading_dataframes - INFO - Processing signal: TRADE - BUY for SENSEX
2025-09-14 12:32:37 - trading_dataframes - INFO - Processing signal: TRADE - BUY for SENSEX
2025-09-14 12:32:37 - trading_dataframes - INFO - Processing signal: TRADE - BUY for SENSEX
2025-09-14 12:32:39 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:32:39 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:32:39 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25000 CALL at price 165.8
2025-09-14 12:32:39 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:32:39 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:32:39 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25100 PUT at price 75.5
2025-09-14 12:32:39 - trading_dataframes - WARNING - Could not retrieve market price for SENSEX
2025-09-14 12:32:39 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:32:39 - trading_dataframes - INFO - Created new trade entry for SENSEX 18 SEP 82000 PUT at price 152.9
2025-09-14 12:32:39 - trading_dataframes - WARNING - Could not retrieve market price for SENSEX
2025-09-14 12:32:39 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:32:39 - trading_dataframes - INFO - Created new trade entry for SENSEX 18 SEP 82500 PUT at price 135.6
2025-09-14 12:32:39 - trading_dataframes - WARNING - Could not retrieve market price for SENSEX
2025-09-14 12:32:39 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:32:39 - trading_dataframes - INFO - Created new trade entry for SENSEX 18 SEP 83000 PUT at price 115.8
2025-09-14 12:32:39 - trading_dataframes - WARNING - Could not retrieve market price for SENSEX
2025-09-14 12:32:39 - trading_dataframes - WARNING - Could not retrieve market price for SENSEX
2025-09-14 12:32:39 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:32:39 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:32:39 - trading_dataframes - INFO - Created new trade entry for SENSEX 18 SEP 81000 CALL at price 195.3
2025-09-14 12:32:39 - trading_dataframes - INFO - Created new trade entry for SENSEX 18 SEP 81500 CALL at price 175.4
2025-09-14 12:32:39 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:32:39 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:32:39 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:32:39 - trading_dataframes - WARNING - Could not retrieve market price for NIFTY
2025-09-14 12:32:39 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 24900 PUT at price 85.3
2025-09-14 12:32:39 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:32:39 - trading_dataframes - INFO - Using signal price as fallback for actual price
2025-09-14 12:32:39 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25200 CALL at price 145.2
2025-09-14 12:32:39 - trading_dataframes - INFO - Created new trade entry for NIFTY 16 SEP 25300 CALL at price 125.7
2025-09-14 12:33:05 - trading_dataframes - INFO - Instrument files updated successfully
2025-09-14 12:33:05 - trading_dataframes - INFO - Reloaded instruments in API handler
2025-09-14 12:33:05 - trading_dataframes - INFO - Trading Dataframe Manager initialized with market data integration
