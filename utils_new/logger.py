import logging
import os
from datetime import datetime

class Logger:
    _instance = None
    _loggers = {}

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def setup(self, log_dir="logs"):
        """Setup base logging configuration"""
        # Create base log directory
        os.makedirs(log_dir, exist_ok=True)
        
        # Create date-specific directory
        today = datetime.now().strftime('%Y-%m-%d')
        daily_log_dir = os.path.join(log_dir, today)
        os.makedirs(daily_log_dir, exist_ok=True)
        
        # Main application logger
        self._setup_logger('main', os.path.join(daily_log_dir, 'app.log'))
        # Trading logger
        self._setup_logger('trade', os.path.join(daily_log_dir, 'trades.log'))
        # API logger
        self._setup_logger('api', os.path.join(daily_log_dir, 'api.log'))

    def _setup_logger(self, name, filepath):
        """Setup individual logger with file and console handlers"""
        logger = logging.getLogger(name)
        logger.setLevel(logging.INFO)
        
        # Clear any existing handlers
        logger.handlers = []
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # File handler
        file_handler = logging.FileHandler(filepath)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        self._loggers[name] = logger
        return logger

    def get_logger(self, name):
        """Get logger by name. Creates new logger if it doesn't exist."""
        if name not in self._loggers:
            self._setup_logger(name, f"logs/{name}.log")
        return self._loggers[name]

    def log_and_print(self, name, message, level="info"):
        """Unified logging and printing method"""
        logger = self.get_logger(name)
        log_method = getattr(logger, level.lower())
        log_method(message)

logger = Logger()  # Singleton instance
