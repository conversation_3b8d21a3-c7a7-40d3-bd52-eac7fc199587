#!/usr/bin/env python3
"""
Enhanced Algo Trading Bot
Comprehensive trading system with order management, risk controls, and monitoring
"""

import asyncio
import sys
import signal
from datetime import datetime, timedelta
from telethon import TelegramClient, events
from trading_config import config
from utils.logger import logger
from utils.signal_handler import SignalHandler

class TradingBot:
    """Main trading bot class with enhanced functionality"""
    
    def __init__(self):
        self.logger = logger.get_logger('main')
        self.signal_handler = SignalHandler()
        self.client = None
        self.running = False
        self.last_heartbeat = datetime.now()
        
    async def initialize(self):
        """Initialize the trading bot"""
        try:
            # Setup logging
            logger.setup(log_dir=config.LOG_DIR)
            
            # Validate configuration
            if not config.validate():
                self.logger.error("Invalid configuration. Please check your settings.")
                return False
                
            # Initialize Telegram client
            self.client = TelegramClient(config.SESSION_NAME, config.API_ID, config.API_HASH)
            
            logger.log_trade_event('main', "🚀 Enhanced Algo Trading Bot Starting...")
            logger.log_trade_event('main', f"Trading Mode: {'LIVE' if config.ENABLE_ACTUAL_TRADING else 'SIMULATION'}")
            logger.log_trade_event('main', f"Default Lot Size: {config.DEFAULT_LOT_SIZE}")
            logger.log_trade_event('main', f"Signal Timeout: {config.SIGNAL_TIMEOUT_SECONDS}s")
            
            await self.client.start()
            
            # Get bot info
            me = await self.client.get_me()
            logger.log_trade_event('main', f"✅ Bot authenticated as {me.username}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Initialization failed: {e}")
            return False
    
    async def find_target_group(self):
        """Find and validate the target Telegram group"""
        try:
            logger.log_trade_event('main', f"🔍 Searching for group: {config.GROUP_NAME}")
            
            async for dialog in self.client.iter_dialogs():
                if dialog.name == config.GROUP_NAME:
                    logger.log_trade_event('main', f"✅ Found target group: {config.GROUP_NAME}")
                    return dialog.entity
                    
            self.logger.error(f"❌ Group '{config.GROUP_NAME}' not found")
            self.logger.error("Please ensure you are a member of the group and the name is correct")
            return None
            
        except Exception as e:
            self.logger.error(f"Error finding target group: {e}")
            return None
    
    async def setup_message_handler(self, target_group):
        """Setup the message handler for the target group"""
        @self.client.on(events.NewMessage(chats=target_group))
        async def message_handler(event):
            """Handle incoming messages from the specified group"""
            try:
                self.last_heartbeat = datetime.now()
                
                # Process the message
                result = await self.signal_handler.handle_telegram_message(event.message.text)
                
                # Log the result
                if result["status"] == "error":
                    self.logger.error(f"❌ {result['message']}")
                elif result["status"] == "success":
                    logger.log_trade_event('main', f"✅ {result['message']}")
                else:
                    # Info or warning messages
                    logger.log_trade_event('main', f"ℹ️ {result['message']}")
                    
            except Exception as e:
                self.logger.error(f"❌ Error in message handler: {e}")
    
    async def start_monitoring_tasks(self):
        """Start background monitoring tasks"""
        async def position_monitor():
            """Monitor positions for timeouts and risk management"""
            while self.running:
                try:
                    if config.ENABLE_POSITION_MONITORING:
                        # Check for position timeouts
                        timeout_results = self.signal_handler.check_position_timeouts()
                        for result in timeout_results:
                            if result.get('status') == 'success':
                                logger.log_trade_event('main', f"⏰ Auto-closed: {result.get('message', 'Unknown')}")
                    
                    # Sleep for 10 seconds before next check
                    await asyncio.sleep(10)
                    
                except Exception as e:
                    self.logger.error(f"Position monitoring error: {e}")
                    await asyncio.sleep(30)  # Wait longer on error
        
        async def heartbeat_monitor():
            """Monitor system heartbeat and log status"""
            while self.running:
                try:
                    # Log system status every 5 minutes
                    await asyncio.sleep(300)
                    
                    status = self.signal_handler.get_trading_status()
                    if status.get('status') == 'success':
                        data = status['data']
                        logger.log_trade_event('main', 
                            f"💓 System Status - Market: {'OPEN' if data['market_open'] else 'CLOSED'}, "
                            f"Positions: {data['active_positions'].get('total_positions', 0)}")
                    
                except Exception as e:
                    self.logger.error(f"Heartbeat monitoring error: {e}")
        
        # Start monitoring tasks
        if config.ENABLE_POSITION_MONITORING:
            asyncio.create_task(position_monitor())
        asyncio.create_task(heartbeat_monitor())
    
    async def run(self):
        """Main run method"""
        try:
            # Initialize the bot
            if not await self.initialize():
                return False
            
            # Find target group
            target_group = await self.find_target_group()
            if not target_group:
                return False
            
            # Setup message handler
            await self.setup_message_handler(target_group)
            
            # Start monitoring tasks
            self.running = True
            await self.start_monitoring_tasks()
            
            logger.log_trade_event('main', "🔄 Bot is now listening for trading signals...")
            
            # Run until disconnected
            await self.client.run_until_disconnected()
            
        except Exception as e:
            self.logger.error(f"❌ Bot crashed: {e}")
            return False
        finally:
            self.running = False
    
    def shutdown(self):
        """Graceful shutdown"""
        self.running = False
        logger.log_trade_event('main', "🛑 Trading bot shutting down...")
        
        # Force close all positions if enabled
        if config.ENABLE_AUTO_CLOSE:
            try:
                results = self.signal_handler.force_close_all_positions()
                for result in results:
                    if result.get('status') == 'success':
                        logger.log_trade_event('main', f"🔒 Emergency close: {result.get('message', 'Unknown')}")
            except Exception as e:
                self.logger.error(f"Error during emergency position closure: {e}")

def signal_handler_func(signum, frame):
    """Handle system signals for graceful shutdown"""
    print("\n🛑 Received shutdown signal...")
    sys.exit(0)

async def main():
    """Main entry point"""
    # Setup signal handlers
    signal.signal(signal.SIGINT, signal_handler_func)
    signal.signal(signal.SIGTERM, signal_handler_func)
    
    # Create and run the bot
    bot = TradingBot()
    
    try:
        await bot.run()
    except KeyboardInterrupt:
        logger.log_trade_event('main', "🛑 Bot stopped by user")
        bot.shutdown()
    except Exception as e:
        logger.log_error('main', f"❌ Unexpected error: {e}")
        bot.shutdown()
        sys.exit(1)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Trading bot stopped by user")
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)
