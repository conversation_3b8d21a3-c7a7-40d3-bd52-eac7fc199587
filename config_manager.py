#!/usr/bin/env python3
"""
Trading Configuration Manager
Simple utility to manage trading system configuration and modes
"""

import sys
import argparse
from trading_config import config

def show_current_config():
    """Display current configuration"""
    print("🔧 Current Trading System Configuration")
    print("=" * 60)
    
    # Trading mode
    mode_desc = config.get_mode_description()
    print(f"Trading Mode: {mode_desc}")
    print()
    
    # Core settings
    print("📊 Core Settings:")
    print(f"  • Default Lot Size: {config.DEFAULT_LOT_SIZE}")
    print(f"  • Signal Timeout: {config.SIGNAL_TIMEOUT_SECONDS}s")
    print(f"  • Max Positions: {config.MAX_POSITIONS}")
    print(f"  • Max Daily Loss: ₹{config.MAX_DAILY_LOSS:,.2f}")
    print()
    
    # System flags
    print("⚙️  System Flags:")
    print(f"  • Actual Trading: {'✅ ENABLED' if config.ENABLE_ACTUAL_TRADING else '❌ DISABLED'}")
    print(f"  • Market Data Fetching: {'✅ ENABLED' if config.ENABLE_MARKET_DATA_FETCHING else '❌ DISABLED'}")
    print(f"  • Live Prices in Simulation: {'✅ ENABLED' if config.USE_LIVE_PRICES_IN_SIMULATION else '❌ DISABLED'}")
    print(f"  • CSV Logging: {'✅ ENABLED' if config.ENABLE_CSV_LOGGING else '❌ DISABLED'}")
    print(f"  • Position Monitoring: {'✅ ENABLED' if config.ENABLE_POSITION_MONITORING else '❌ DISABLED'}")
    print(f"  • Auto Close: {'✅ ENABLED' if config.ENABLE_AUTO_CLOSE else '❌ DISABLED'}")
    print()
    
    # Market settings
    print("🕐 Market Settings:")
    print(f"  • Trading Hours: {config.TRADING_START_TIME} - {config.TRADING_END_TIME}")
    print(f"  • Market Currently: {'🟢 OPEN' if config.is_market_open() else '🔴 CLOSED'}")
    print()
    
    # File paths
    print("📁 File Paths:")
    print(f"  • Instruments: {config.INSTRUMENT_FILE_PATH}")
    print(f"  • Logs: {config.LOG_DIR}")
    print(f"  • Trade Logs: {config.TRADE_LOG_DIR}")
    print()
    
    print("=" * 60)

def enable_live_trading():
    """Enable live trading mode with safety warnings"""
    print("⚠️  WARNING: ENABLING LIVE TRADING MODE")
    print("⚠️  This will place REAL orders with REAL money!")
    print()
    print("Before enabling live trading, ensure you have:")
    print("  ✓ Tested thoroughly in simulation mode")
    print("  ✓ Verified your SmartAPI credentials are correct")
    print("  ✓ Set appropriate lot sizes and risk limits")
    print("  ✓ Understood all risks involved")
    print("  ✓ Have sufficient funds in your trading account")
    print()
    
    response = input("Are you sure you want to enable LIVE TRADING? (type 'YES' to confirm): ")
    
    if response.strip().upper() == 'YES':
        # Modify the configuration file
        try:
            with open('trading_config.py', 'r') as f:
                content = f.read()
            
            # Replace the ENABLE_ACTUAL_TRADING line
            content = content.replace(
                'ENABLE_ACTUAL_TRADING: bool = False',
                'ENABLE_ACTUAL_TRADING: bool = True'
            )
            
            with open('trading_config.py', 'w') as f:
                f.write(content)
            
            print("✅ Live trading mode ENABLED in trading_config.py")
            print("🔴 RESTART the trading bot for changes to take effect")
            print("🔴 The system will now place REAL orders!")
            
        except Exception as e:
            print(f"❌ Error updating configuration: {e}")
            return False
    else:
        print("❌ Live trading mode NOT enabled")
        return False
    
    return True

def enable_simulation_mode():
    """Enable simulation mode (safe testing)"""
    print("✅ Enabling simulation mode...")
    print("✅ Orders will NOT be placed, but market data will be fetched")
    print("✅ All signals will be processed and logged")
    print("✅ CSV files will contain real market prices")
    
    try:
        with open('trading_config.py', 'r') as f:
            content = f.read()
        
        # Replace the ENABLE_ACTUAL_TRADING line
        content = content.replace(
            'ENABLE_ACTUAL_TRADING: bool = True',
            'ENABLE_ACTUAL_TRADING: bool = False'
        )
        
        with open('trading_config.py', 'w') as f:
            f.write(content)
        
        print("✅ Simulation mode ENABLED in trading_config.py")
        print("🟡 RESTART the trading bot for changes to take effect")
        print("🟡 The system will now run in safe simulation mode")
        
    except Exception as e:
        print(f"❌ Error updating configuration: {e}")
        return False
    
    return True

def update_lot_size(new_lot_size):
    """Update the default lot size"""
    try:
        new_lot_size = int(new_lot_size)
        if new_lot_size < 1:
            print("❌ Lot size must be at least 1")
            return False
        
        with open('trading_config.py', 'r') as f:
            content = f.read()
        
        # Find and replace the DEFAULT_LOT_SIZE line
        import re
        pattern = r'DEFAULT_LOT_SIZE: int = \d+'
        replacement = f'DEFAULT_LOT_SIZE: int = {new_lot_size}'
        content = re.sub(pattern, replacement, content)
        
        with open('trading_config.py', 'w') as f:
            f.write(content)
        
        print(f"✅ Default lot size updated to {new_lot_size}")
        print("🔄 RESTART the trading bot for changes to take effect")
        
    except ValueError:
        print("❌ Invalid lot size. Please enter a number.")
        return False
    except Exception as e:
        print(f"❌ Error updating lot size: {e}")
        return False
    
    return True

def main():
    """Main CLI interface"""
    parser = argparse.ArgumentParser(description='Trading Configuration Manager')
    parser.add_argument('command', choices=[
        'show', 'live', 'sim', 'simulation', 'lot-size'
    ], help='Command to execute')
    
    parser.add_argument('--size', type=int, help='New lot size (for lot-size command)')
    
    args = parser.parse_args()
    
    try:
        if args.command == 'show':
            show_current_config()
        elif args.command == 'live':
            enable_live_trading()
        elif args.command in ['sim', 'simulation']:
            enable_simulation_mode()
        elif args.command == 'lot-size':
            if args.size:
                update_lot_size(args.size)
            else:
                print("❌ Please specify lot size with --size parameter")
                print("Example: python config_manager.py lot-size --size 2")
    
    except KeyboardInterrupt:
        print("\n🛑 Operation cancelled by user")
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
