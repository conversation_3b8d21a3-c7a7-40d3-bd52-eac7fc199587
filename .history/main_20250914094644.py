import asyncio
import sys
from telethon import TelegramClient, events
from utils_new.config import config
from utils_new.logger import logger
from utils_new.signal_handler import SignalHandler

# Initialize logger
logger.setup(log_dir="logs")
main_logger = logger.get_logger('main')

# Initialize signal handler
signal_handler = SignalHandler()

async def main():
    """Main entry point for the trading bot"""
    try:
        # Validate configuration
        if not config.validate():
            main_logger.error("Invalid configuration. Please check your environment variables.")
            sys.exit(1)

        # Initialize Telegram client
        client = TelegramClient(config.SESSION_NAME, config.API_ID, config.API_HASH)
        
        main_logger.info("🚀 Starting Enhanced Algo Trading Bot...")
        await client.start()
        
        # Get bot info
        me = await client.get_me()
        main_logger.info(f"✅ Bot started as {me.username}")
        
        # Find target group
        target_group = None
        main_logger.info("🔍 Fetching dialogs to find the group...")
        
        async for dialog in client.iter_dialogs():
            if dialog.name == config.GROUP_NAME:
                target_group = dialog.entity
                break
                
        if not target_group:
            main_logger.error(f"❌ Error: Group '{config.GROUP_NAME}' not found")
            main_logger.error("Please ensure you are a member of the group and the name is correct")
            return
            
        main_logger.info(f"✅ Successfully found group '{config.GROUP_NAME}'")
        
        @client.on(events.NewMessage(chats=target_group))
        async def message_handler(event):
            """Handle incoming messages from the specified group"""
            try:
                # Process the message
                result = await signal_handler.handle_telegram_message(event.message.text)
                
                # Log the result
                if result["status"] == "error":
                    main_logger.error(f"❌ {result['message']}")
                else:
                    main_logger.info(f"✅ {result['message']}")
                    
            except Exception as e:
                main_logger.error(f"❌ Error in message handler: {e}")

        main_logger.info("🔄 Listening for messages...")
        await client.run_until_disconnected()
        
    except Exception as e:
        main_logger.error(f"❌ Bot crashed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        main_logger.info("🛑 Bot stopped by user")
    except Exception as e:
        main_logger.error(f"❌ Unexpected error: {e}")
        sys.exit(1)
