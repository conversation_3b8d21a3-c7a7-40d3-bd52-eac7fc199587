#!/usr/bin/env python3
"""
Quick Test Script for Your Specific Trading Messages
Tests the exact sequence of messages you provided to validate system behavior
"""

import os
import sys
import time
import logging
from datetime import datetime

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils_new.signal_handler import SignalHandler
from utils_new.api_handler import APIHandler
from utils_new.trading_dataframe_manager import TradingDataframeManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('tests/your_messages_test.log'),
        logging.StreamHandler()
    ]
)

def test_your_specific_messages():
    """Test your specific NIFTY PUT trading sequence"""
    
    print("🎯 Testing Your Specific NIFTY PUT Trading Messages")
    print("=" * 60)
    
    # Your exact message sequence
    your_messages = [
        # We need to add the initial BUY signal to start the trade
        "===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 PUT$30.6$28.6$2025-09-12 14:48:00+05:30",
        
        # Your provided messages
        "===Algo_Trading===$INTIMATION$Continue to hold trade for $NIFTY 16 SEP 25000 PUT at current price: 31.75 (Entry price: 30.6) (Stop loss: 30.16)",
        "===Algo_Trading===$Update$STOP LOSS to$30.25$for option$NIFTY 16 SEP 25000 PUT current price: 31.85 (Entry price: 30.6)",
        "===Algo_Trading===$INTIMATION$Continue to hold trade for $NIFTY 16 SEP 25000 PUT at current price: 31.85 (Entry price: 30.6) (Stop loss: 30.25)",
        "===Algo_Trading===$INTIMATION$Continue to hold trade for $NIFTY 16 SEP 25000 PUT at current price: 32.0 (Entry price: 30.6) (Stop loss: 30.25)",
        "===Algo_Trading===$Update$STOP LOSS to$30.27$for option$NIFTY 16 SEP 25000 PUT current price: 32.4 (Entry price: 30.6)",
        "===Algo_Trading===$INTIMATION$Continue to hold trade for $NIFTY 16 SEP 25000 PUT at current price: 32.4 (Entry price: 30.6) (Stop loss: 30.27)",
        "===Algo_Trading===$INTIMATION$Continue to hold trade for $NIFTY 16 SEP 25000 PUT at current price: 32.15 (Entry price: 30.6) (Stop loss: 30.27)",
        "===Algo_Trading===$Update$STOP LOSS to$30.33$for option$NIFTY 16 SEP 25000 PUT current price: 31.25 (Entry price: 30.6)",
        "===Algo_Trading===$INTIMATION$Continue to hold trade for $NIFTY 16 SEP 25000 PUT at current price: 31.25 (Entry price: 30.6) (Stop loss: 30.33)",
        "===Algo_Trading===$INTIMATION$Continue to hold trade for $NIFTY 16 SEP 25000 PUT at current price: 30.5 (Entry price: 30.6) (Stop loss: 30.33)",
        "===Algo_Trading===$TRADE$CLOSE$NIFTY 16 SEP 25000 PUT$30.05$2025-09-12 15:00:00+05:30$stop_loss_exit$30.33",
        "===Algo_Trading===$INTIMATION$Trade_log$Instrument_name        trade_entry_time          trade_exit_time            Trade_entry_price Trade_exit_price profit_loss\nNIFTY 16 SEP 25000 PUT 2025-09-12 14:48:00+05:30 2025-09-12 15:00:00+05:30 30.6                30.33           -80.25"
    ]
    
    # Initialize handlers directly
    signal_handler = SignalHandler()

    # Process the sequence
    print(f"📨 Processing {len(your_messages)} messages in sequence...")
    print(f"⏱️ Adding 0.5 second delay between messages to simulate real trading...")
    print()

    # Process each message
    results = []
    start_time = time.time()

    for i, signal_text in enumerate(your_messages):
        signal_start = time.time()

        try:
            print(f"📨 Message {i+1}/{len(your_messages)}: {signal_text[:60]}...")

            # Parse signal
            parsed_signal = signal_handler.parse_message(signal_text)

            if parsed_signal:
                # Process through dataframe manager
                df_result = signal_handler.dataframe_manager.process_signal(parsed_signal)

                # Process through API handler (only for TRADE actions)
                api_result = None
                if parsed_signal.action in ['BUY', 'SELL', 'CLOSE']:
                    api_result = signal_handler.api.process_signal(parsed_signal)

                processing_time = time.time() - signal_start

                result = {
                    "signal_number": i + 1,
                    "signal_text": signal_text,
                    "parsed": True,
                    "symbol": parsed_signal.symbol,
                    "action": parsed_signal.action,
                    "signal_type": parsed_signal.signal_type,
                    "processing_time": processing_time,
                    "dataframe_processed": df_result is not None,
                    "api_processed": api_result is not None,
                    "api_success": api_result.get('status') == 'success' if api_result else None,
                    "timestamp": datetime.now().isoformat()
                }

                # Extract specific details based on signal type
                if parsed_signal.signal_type == "INTIMATION":
                    result["current_price"] = getattr(parsed_signal, 'current_price', None)
                    result["entry_price"] = getattr(parsed_signal, 'entry_price', None)
                    result["stop_loss"] = getattr(parsed_signal, 'stop_loss', None)
                elif parsed_signal.signal_type == "UPDATE":
                    result["new_stop_loss"] = getattr(parsed_signal, 'new_stop_loss', None)
                    result["update_type"] = getattr(parsed_signal, 'update_type', None)
                elif parsed_signal.action in ['BUY', 'CLOSE']:
                    result["price"] = getattr(parsed_signal, 'price', None)
                    result["strike"] = getattr(parsed_signal, 'strike', None)
                    result["option_type"] = getattr(parsed_signal, 'option_type', None)

                print(f"✅ Processed: {parsed_signal.symbol} {parsed_signal.action} ({processing_time:.3f}s)")

            else:
                result = {
                    "signal_number": i + 1,
                    "signal_text": signal_text,
                    "parsed": False,
                    "error": "Failed to parse signal",
                    "processing_time": time.time() - signal_start,
                    "timestamp": datetime.now().isoformat()
                }
                print(f"❌ Failed to parse message {i+1}")

            results.append(result)

            # Add delay between signals (except for last signal)
            if i < len(your_messages) - 1:
                time.sleep(0.5)

        except Exception as e:
            result = {
                "signal_number": i + 1,
                "signal_text": signal_text,
                "parsed": False,
                "error": str(e),
                "processing_time": time.time() - signal_start,
                "timestamp": datetime.now().isoformat()
            }
            results.append(result)
            print(f"❌ Exception processing message {i+1}: {e}")

    # Create results summary
    total_time = time.time() - start_time
    successful_signals = len([r for r in results if r.get('parsed', False)])

    results_summary = {
        "total_signals": len(your_messages),
        "successful_signals": successful_signals,
        "failed_signals": len(your_messages) - successful_signals,
        "success_rate": (successful_signals / len(your_messages) * 100) if your_messages else 0,
        "total_processing_time": total_time,
        "average_signal_time": total_time / len(your_messages) if your_messages else 0,
        "signal_results": results
    }
    total_time = time.time() - start_time
    
    # Print detailed results
    print(f"\n📊 RESULTS SUMMARY")
    print(f"{'='*50}")
    print(f"Total Messages: {results['total_signals']}")
    print(f"Successfully Processed: {results['successful_signals']}")
    print(f"Failed: {results['failed_signals']}")
    print(f"Success Rate: {results['success_rate']:.1f}%")
    print(f"Total Time: {total_time:.2f} seconds")
    print(f"Average Time per Message: {results['average_signal_time']:.3f} seconds")
    
    # Print message-by-message results
    print(f"\n📝 MESSAGE-BY-MESSAGE RESULTS:")
    print(f"{'='*80}")
    
    for i, signal_result in enumerate(results['signal_results'], 1):
        status = "✅" if signal_result.get('parsed', False) else "❌"
        action = signal_result.get('action', 'UNKNOWN')
        signal_type = signal_result.get('signal_type', 'UNKNOWN')
        processing_time = signal_result.get('processing_time', 0)
        
        print(f"{status} Message {i:2d}: {action:8s} | {signal_type:12s} | {processing_time:.3f}s")
        
        # Show additional details for specific signal types
        if signal_result.get('parsed'):
            if signal_type == "INTIMATION":
                current_price = signal_result.get('current_price')
                entry_price = signal_result.get('entry_price')
                stop_loss = signal_result.get('stop_loss')
                if current_price and entry_price and stop_loss:
                    print(f"           Current: {current_price} | Entry: {entry_price} | SL: {stop_loss}")
            
            elif signal_type == "UPDATE":
                new_sl = signal_result.get('new_stop_loss')
                if new_sl:
                    print(f"           New Stop Loss: {new_sl}")
            
            elif action in ['BUY', 'CLOSE']:
                price = signal_result.get('price')
                strike = signal_result.get('strike')
                option_type = signal_result.get('option_type')
                if price and strike and option_type:
                    print(f"           {strike} {option_type} @ {price}")
        
        # Show errors if any
        if not signal_result.get('parsed', False):
            error = signal_result.get('error', 'Unknown error')
            print(f"           ERROR: {error}")
        
        print()
    
    # Analyze the trade sequence
    print(f"🔍 TRADE SEQUENCE ANALYSIS:")
    print(f"{'='*50}")
    
    # Count signal types
    signal_counts = {}
    for result in results['signal_results']:
        if result.get('parsed'):
            action = result.get('action', 'UNKNOWN')
            signal_counts[action] = signal_counts.get(action, 0) + 1
    
    print(f"Signal Type Breakdown:")
    for signal_type, count in signal_counts.items():
        print(f"  {signal_type}: {count} signals")
    
    # Check for proper trade lifecycle
    actions = [r.get('action') for r in results['signal_results'] if r.get('parsed')]
    
    if 'BUY' in actions and 'CLOSE' in actions:
        buy_index = actions.index('BUY')
        close_index = actions.index('CLOSE')
        
        print(f"\n✅ Complete trade lifecycle detected:")
        print(f"  BUY signal at position {buy_index + 1}")
        print(f"  CLOSE signal at position {close_index + 1}")
        print(f"  Trade duration: {close_index - buy_index} signals")
        
        # Count INTIMATION and UPDATE signals between BUY and CLOSE
        middle_actions = actions[buy_index+1:close_index]
        intimations = middle_actions.count('HOLD')
        updates = middle_actions.count('UPDATE')
        
        print(f"  INTIMATION signals: {intimations}")
        print(f"  UPDATE signals: {updates}")
        
        if intimations > 0 or updates > 0:
            print(f"  ✅ Proper trade management signals present")
        else:
            print(f"  ⚠️ No trade management signals between BUY and CLOSE")
    
    else:
        print(f"⚠️ Incomplete trade lifecycle - missing BUY or CLOSE signal")
    
    # Final assessment
    print(f"\n🎯 FINAL ASSESSMENT:")
    print(f"{'='*50}")
    
    if results['success_rate'] >= 95:
        print(f"🎉 EXCELLENT! Your message sequence is processed perfectly!")
        print(f"✅ System handles all signal types correctly")
        print(f"✅ Trade lifecycle is properly managed")
        print(f"✅ Ready for live trading with these message formats")
    
    elif results['success_rate'] >= 85:
        print(f"✅ GOOD! Most messages processed successfully")
        print(f"🔧 Minor issues to address before live trading")
        print(f"📊 Review failed messages and fix parsing logic")
    
    else:
        print(f"❌ ISSUES DETECTED! Significant problems with message processing")
        print(f"🛠️ Major fixes needed before live trading")
        print(f"🧪 Re-test after implementing fixes")
    
    # Save detailed results
    import json
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"tests/your_messages_results_{timestamp}.json"
    
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n📄 Detailed results saved to: {results_file}")
    
    return results


def main():
    """Main execution"""
    results = test_your_specific_messages()
    
    print(f"\n🏁 Test completed!")
    print(f"Check the log file: tests/your_messages_test.log for detailed logs")
    
    return results


if __name__ == "__main__":
    main()
