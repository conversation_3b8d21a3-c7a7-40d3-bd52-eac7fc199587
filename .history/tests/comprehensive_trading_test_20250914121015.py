#!/usr/bin/env python3
"""
Comprehensive Test Suite for Indian Stock Market Trading Bot
Tests all 5 signal formats with concurrent execution capabilities
Validates NIFTY and SENSEX options with proper lot sizes
"""

import asyncio
import time
import threading
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any
import pandas as pd
import os
import sys

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils_new.signal_handler import SignalHandler
from utils_new.api_handler import APIHandler, TradeSignal
from utils_new.trading_dataframe_manager import TradingDataframeManager
from utils_new.config import config

# Configure logging for tests
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('tests/test_results.log'),
        logging.StreamHandler()
    ]
)

class ComprehensiveTradingTest:
    """
    Comprehensive test suite for the trading bot with concurrent execution testing
    """
    
    def __init__(self):
        self.logger = logging.getLogger('TradingTest')
        self.test_results = []
        self.concurrent_results = []
        self.signal_handler = None
        self.api_handler = None
        self.dataframe_manager = None
        
        # Test signal data - All 5 formats with NIFTY and SENSEX
        self.test_signals = {
            "format1_buy": [
                "===Algo_Trading===$TRADE$BUY$SENSEX 18 SEP 81500 PUT$152.9$146.3$2025-09-12 14:33:00+05:30",
                "===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 PUT$30.6$28.6$2025-09-12 14:48:00+05:30",
                "===Algo_Trading===$TRADE$BUY$SENSEX 18 SEP 82000 CALL$145.2$140.1$2025-09-12 14:50:00+05:30",
                "===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 24800 CALL$165.8$160.2$2025-09-12 14:52:00+05:30"
            ],
            "format2_intimation": [
                "===Algo_Trading===$INTIMATION$Continue to hold trade for $SENSEX 18 SEP 81500 PUT at current price: 151.2 (Entry price: 152.9) (Stop loss: 146.3)",
                "===Algo_Trading===$INTIMATION$Continue to hold trade for $NIFTY 16 SEP 25000 PUT at current price: 30.05 (Entry price: 30.6) (Stop loss: 28.6)",
                "===Algo_Trading===$INTIMATION$Continue to hold trade for $SENSEX 18 SEP 82000 CALL at current price: 147.8 (Entry price: 145.2) (Stop loss: 140.1)",
                "===Algo_Trading===$INTIMATION$Continue to hold trade for $NIFTY 16 SEP 24800 CALL at current price: 168.2 (Entry price: 165.8) (Stop loss: 160.2)"
            ],
            "format3_close": [
                "===Algo_Trading===$TRADE$CLOSE$SENSEX 18 SEP 81500 PUT$157.4$2025-09-12 14:35:00+05:30$stop_loss_exit$157.4",
                "===Algo_Trading===$TRADE$CLOSE$NIFTY 16 SEP 25000 PUT$30.05$2025-09-12 15:00:00+05:30$stop_loss_exit$30.33",
                "===Algo_Trading===$TRADE$CLOSE$SENSEX 18 SEP 82000 CALL$148.9$2025-09-12 15:05:00+05:30$profit_booking$148.9",
                "===Algo_Trading===$TRADE$CLOSE$NIFTY 16 SEP 24800 CALL$170.5$2025-09-12 15:10:00+05:30$profit_booking$170.5"
            ],
            "format4_update": [
                "===Algo_Trading===$Update$STOP LOSS to$154.08$for option$SENSEX 18 SEP 81500 PUT current price: 157.8 (Entry price: 155.3)",
                "===Algo_Trading===$Update$STOP LOSS to$30.01$for option$NIFTY 16 SEP 25000 PUT",
                "===Algo_Trading===$Update$STOP LOSS to$30.13$for option$NIFTY 16 SEP 25000 PUT current price: 31.05 (Entry price: 30.6)",
                "===Algo_Trading===$Update$STOP LOSS to$142.5$for option$SENSEX 18 SEP 82000 CALL current price: 147.2 (Entry price: 145.2)"
            ],
            "format5_crossover": [
                "===Algo_Trading===$TRADE$CLOSE$NIFTY 16 SEP 24700 PUT$82.35$2025-09-08 15:15:00+05:30$opposite_crossover_exit",
                "===Algo_Trading===$TRADE$CLOSE$SENSEX 18 SEP 81000 CALL$156.75$2025-09-08 15:20:00+05:30$opposite_crossover_exit"
            ]
        }
        
        # Expected lot sizes
        self.expected_lot_sizes = {
            "NIFTY": 75,
            "SENSEX": 20  # Based on instrument data analysis
        }
    
    def setup_test_environment(self):
        """Initialize test environment with mock market conditions"""
        try:
            self.logger.info("🔧 Setting up test environment...")
            
            # Initialize handlers
            self.api_handler = APIHandler()
            self.signal_handler = SignalHandler(self.api_handler)
            self.dataframe_manager = TradingDataframeManager(
                smart_api=self.api_handler.smart_api,
                api_handler=self.api_handler
            )
            
            # Create test directories
            os.makedirs('tests/test_logs', exist_ok=True)
            os.makedirs('tests/concurrent_logs', exist_ok=True)
            
            self.logger.info("✅ Test environment setup complete")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to setup test environment: {e}")
            return False
    
    def test_signal_parsing(self) -> Dict[str, Any]:
        """Test parsing of all 5 signal formats"""
        self.logger.info("🧪 Testing signal parsing for all formats...")
        
        parsing_results = {
            "format1_buy": [],
            "format2_intimation": [],
            "format3_close": [],
            "format4_update": [],
            "format5_crossover": []
        }
        
        for format_type, signals in self.test_signals.items():
            self.logger.info(f"Testing {format_type}...")
            
            for signal_text in signals:
                try:
                    parsed_signal = self.signal_handler.parse_message(signal_text)
                    
                    if parsed_signal:
                        result = {
                            "signal_text": signal_text,
                            "parsed": True,
                            "symbol": parsed_signal.symbol,
                            "action": parsed_signal.action,
                            "strike": parsed_signal.strike,
                            "option_type": parsed_signal.option_type,
                            "signal_type": parsed_signal.signal_type
                        }
                        self.logger.info(f"✅ Parsed: {parsed_signal.symbol} {parsed_signal.action}")
                    else:
                        result = {
                            "signal_text": signal_text,
                            "parsed": False,
                            "error": "Failed to parse signal"
                        }
                        self.logger.error(f"❌ Failed to parse: {signal_text}")
                    
                    parsing_results[format_type].append(result)
                    
                except Exception as e:
                    result = {
                        "signal_text": signal_text,
                        "parsed": False,
                        "error": str(e)
                    }
                    parsing_results[format_type].append(result)
                    self.logger.error(f"❌ Exception parsing signal: {e}")
        
        return parsing_results
    
    def test_instrument_lookup(self) -> Dict[str, Any]:
        """Test instrument lookup for NIFTY and SENSEX options"""
        self.logger.info("🔍 Testing instrument lookup...")
        
        lookup_results = []
        
        # Test signals for instrument lookup
        test_cases = [
            {"symbol": "NIFTY", "strike": 25000, "option_type": "CE", "expiry": "16 SEP"},
            {"symbol": "NIFTY", "strike": 25000, "option_type": "PE", "expiry": "16 SEP"},
            {"symbol": "SENSEX", "strike": 81500, "option_type": "CE", "expiry": "18 SEP"},
            {"symbol": "SENSEX", "strike": 81500, "option_type": "PE", "expiry": "18 SEP"}
        ]
        
        for case in test_cases:
            try:
                # Create a test signal
                test_signal = TradeSignal(
                    symbol=case["symbol"],
                    action="BUY",
                    strike=case["strike"],
                    option_type=case["option_type"],
                    expiry_date=case["expiry"],
                    timestamp=datetime.now()
                )
                
                # Find instrument
                instrument = self.api_handler.find_instrument(test_signal)
                
                if instrument:
                    lot_size = instrument.get('lot_size', 1)
                    expected_lot_size = self.expected_lot_sizes.get(case["symbol"], 1)
                    
                    result = {
                        "symbol": case["symbol"],
                        "strike": case["strike"],
                        "option_type": case["option_type"],
                        "found": True,
                        "trading_symbol": instrument.get('tradingsymbol'),
                        "lot_size": lot_size,
                        "expected_lot_size": expected_lot_size,
                        "lot_size_correct": lot_size == expected_lot_size
                    }
                    
                    self.logger.info(f"✅ Found: {instrument.get('tradingsymbol')} (Lot: {lot_size})")
                else:
                    result = {
                        "symbol": case["symbol"],
                        "strike": case["strike"],
                        "option_type": case["option_type"],
                        "found": False,
                        "error": "Instrument not found"
                    }
                    self.logger.error(f"❌ Not found: {case}")
                
                lookup_results.append(result)
                
            except Exception as e:
                result = {
                    "symbol": case["symbol"],
                    "strike": case["strike"],
                    "option_type": case["option_type"],
                    "found": False,
                    "error": str(e)
                }
                lookup_results.append(result)
                self.logger.error(f"❌ Exception in lookup: {e}")
        
        return {"instrument_lookups": lookup_results}

    def test_concurrent_signal_processing(self) -> Dict[str, Any]:
        """Test concurrent processing of multiple signals"""
        self.logger.info("🚀 Testing concurrent signal processing...")

        concurrent_results = []

        # Create mixed signals for concurrent testing
        concurrent_signals = [
            "===Algo_Trading===$TRADE$BUY$SENSEX 18 SEP 81500 PUT$152.9$146.3$2025-09-12 14:33:00+05:30",
            "===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 CALL$165.8$160.2$2025-09-12 14:33:05+05:30",
            "===Algo_Trading===$INTIMATION$Continue to hold trade for $SENSEX 18 SEP 82000 CALL at current price: 147.8",
            "===Algo_Trading===$Update$STOP LOSS to$30.01$for option$NIFTY 16 SEP 25000 PUT",
            "===Algo_Trading===$TRADE$CLOSE$SENSEX 18 SEP 81000 CALL$156.75$2025-09-08 15:20:00+05:30$profit_booking"
        ]

        def process_signal_thread(signal_text: str, thread_id: int):
            """Process a single signal in a thread"""
            try:
                start_time = time.time()

                # Parse signal
                parsed_signal = self.signal_handler.parse_message(signal_text)

                if parsed_signal:
                    # Process through dataframe manager
                    df_result = self.dataframe_manager.process_signal(parsed_signal)

                    # Process through API handler
                    api_result = self.api_handler.process_signal(parsed_signal)

                    processing_time = time.time() - start_time

                    result = {
                        "thread_id": thread_id,
                        "signal_text": signal_text[:50] + "...",
                        "symbol": parsed_signal.symbol,
                        "action": parsed_signal.action,
                        "processing_time": processing_time,
                        "dataframe_success": df_result is not None,
                        "api_success": api_result.get('status') == 'success' if api_result else False,
                        "success": True
                    }
                else:
                    result = {
                        "thread_id": thread_id,
                        "signal_text": signal_text[:50] + "...",
                        "success": False,
                        "error": "Failed to parse signal"
                    }

                concurrent_results.append(result)
                self.logger.info(f"Thread {thread_id}: Processed {parsed_signal.symbol if parsed_signal else 'FAILED'}")

            except Exception as e:
                result = {
                    "thread_id": thread_id,
                    "signal_text": signal_text[:50] + "...",
                    "success": False,
                    "error": str(e)
                }
                concurrent_results.append(result)
                self.logger.error(f"Thread {thread_id}: Exception - {e}")

        # Launch concurrent threads
        threads = []
        for i, signal in enumerate(concurrent_signals):
            thread = threading.Thread(target=process_signal_thread, args=(signal, i))
            threads.append(thread)
            thread.start()
            time.sleep(0.1)  # Small delay to simulate real-world timing

        # Wait for all threads to complete
        for thread in threads:
            thread.join(timeout=30)  # 30 second timeout per thread

        return {
            "concurrent_processing": concurrent_results,
            "total_signals": len(concurrent_signals),
            "successful_signals": len([r for r in concurrent_results if r.get('success', False)])
        }

    def test_lot_size_calculations(self) -> Dict[str, Any]:
        """Test lot size calculations for different instruments"""
        self.logger.info("📊 Testing lot size calculations...")

        lot_size_results = []

        test_cases = [
            {"symbol": "NIFTY", "quantity": 1, "expected_units": 75},
            {"symbol": "NIFTY", "quantity": 2, "expected_units": 150},
            {"symbol": "SENSEX", "quantity": 1, "expected_units": 20},
            {"symbol": "SENSEX", "quantity": 3, "expected_units": 60}
        ]

        for case in test_cases:
            try:
                # Create test signal
                test_signal = TradeSignal(
                    symbol=case["symbol"],
                    action="BUY",
                    strike=25000 if case["symbol"] == "NIFTY" else 81500,
                    option_type="CE",
                    expiry_date="16 SEP" if case["symbol"] == "NIFTY" else "18 SEP",
                    quantity=case["quantity"],
                    timestamp=datetime.now()
                )

                # Find instrument to get lot size
                instrument = self.api_handler.find_instrument(test_signal)

                if instrument:
                    lot_size = instrument.get('lot_size', 1)
                    calculated_units = case["quantity"] * lot_size

                    result = {
                        "symbol": case["symbol"],
                        "quantity_lots": case["quantity"],
                        "lot_size": lot_size,
                        "calculated_units": calculated_units,
                        "expected_units": case["expected_units"],
                        "calculation_correct": calculated_units == case["expected_units"]
                    }

                    self.logger.info(f"✅ {case['symbol']}: {case['quantity']} lots = {calculated_units} units")
                else:
                    result = {
                        "symbol": case["symbol"],
                        "quantity_lots": case["quantity"],
                        "error": "Instrument not found"
                    }
                    self.logger.error(f"❌ Instrument not found for {case['symbol']}")

                lot_size_results.append(result)

            except Exception as e:
                result = {
                    "symbol": case["symbol"],
                    "quantity_lots": case["quantity"],
                    "error": str(e)
                }
                lot_size_results.append(result)
                self.logger.error(f"❌ Exception in lot size calculation: {e}")

        return {"lot_size_calculations": lot_size_results}
