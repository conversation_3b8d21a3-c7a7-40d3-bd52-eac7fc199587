#!/usr/bin/env python3
"""
Sequential Trading Test for Indian Stock Market Bot
Tests complete trade lifecycle using real trading messages
Validates proper handling of INTIMATION, UPDATE, and CLOSE signals in sequence
"""

import time
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any
import os
import sys
import json

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils_new.signal_handler import SignalHandler
from utils_new.api_handler import APIHandler
from utils_new.trading_dataframe_manager import TradingDataframeManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('tests/sequential_test.log'),
        logging.StreamHandler()
    ]
)

class SequentialTradingTest:
    """
    Test sequential processing of trading signals in realistic order
    """
    
    def __init__(self):
        self.logger = logging.getLogger('SequentialTest')
        self.results = []
        
        # Initialize handlers
        self.api_handler = APIHandler()
        self.signal_handler = SignalHandler(self.api_handler)
        self.dataframe_manager = TradingDataframeManager(
            smart_api=self.api_handler.smart_api,
            api_handler=self.api_handler
        )
        
        # Complete NIFTY PUT trade sequence from your messages
        self.nifty_put_sequence = [
            # Initial BUY signal (we need to add this to start the trade)
            "===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 25000 PUT$30.6$28.6$2025-09-12 14:48:00+05:30",
            
            # Your provided sequence
            "===Algo_Trading===$INTIMATION$Continue to hold trade for $NIFTY 16 SEP 25000 PUT at current price: 31.75 (Entry price: 30.6) (Stop loss: 30.16)",
            "===Algo_Trading===$Update$STOP LOSS to$30.25$for option$NIFTY 16 SEP 25000 PUT current price: 31.85 (Entry price: 30.6)",
            "===Algo_Trading===$INTIMATION$Continue to hold trade for $NIFTY 16 SEP 25000 PUT at current price: 31.85 (Entry price: 30.6) (Stop loss: 30.25)",
            "===Algo_Trading===$INTIMATION$Continue to hold trade for $NIFTY 16 SEP 25000 PUT at current price: 32.0 (Entry price: 30.6) (Stop loss: 30.25)",
            "===Algo_Trading===$Update$STOP LOSS to$30.27$for option$NIFTY 16 SEP 25000 PUT current price: 32.4 (Entry price: 30.6)",
            "===Algo_Trading===$INTIMATION$Continue to hold trade for $NIFTY 16 SEP 25000 PUT at current price: 32.4 (Entry price: 30.6) (Stop loss: 30.27)",
            "===Algo_Trading===$INTIMATION$Continue to hold trade for $NIFTY 16 SEP 25000 PUT at current price: 32.15 (Entry price: 30.6) (Stop loss: 30.27)",
            "===Algo_Trading===$Update$STOP LOSS to$30.33$for option$NIFTY 16 SEP 25000 PUT current price: 31.25 (Entry price: 30.6)",
            "===Algo_Trading===$INTIMATION$Continue to hold trade for $NIFTY 16 SEP 25000 PUT at current price: 31.25 (Entry price: 30.6) (Stop loss: 30.33)",
            "===Algo_Trading===$INTIMATION$Continue to hold trade for $NIFTY 16 SEP 25000 PUT at current price: 30.5 (Entry price: 30.6) (Stop loss: 30.33)",
            "===Algo_Trading===$TRADE$CLOSE$NIFTY 16 SEP 25000 PUT$30.05$2025-09-12 15:00:00+05:30$stop_loss_exit$30.33",
            "===Algo_Trading===$INTIMATION$Trade_log$Instrument_name        trade_entry_time          trade_exit_time            Trade_entry_price Trade_exit_price profit_loss\nNIFTY 16 SEP 25000 PUT 2025-09-12 14:48:00+05:30 2025-09-12 15:00:00+05:30 30.6                30.33           -80.25"
        ]
        
        # Additional test sequences for SENSEX and mixed scenarios
        self.sensex_call_sequence = [
            "===Algo_Trading===$TRADE$BUY$SENSEX 18 SEP 81500 CALL$152.9$146.3$2025-09-12 14:33:00+05:30",
            "===Algo_Trading===$INTIMATION$Continue to hold trade for $SENSEX 18 SEP 81500 CALL at current price: 155.2 (Entry price: 152.9) (Stop loss: 146.3)",
            "===Algo_Trading===$Update$STOP LOSS to$150.5$for option$SENSEX 18 SEP 81500 CALL current price: 158.7 (Entry price: 152.9)",
            "===Algo_Trading===$INTIMATION$Continue to hold trade for $SENSEX 18 SEP 81500 CALL at current price: 160.1 (Entry price: 152.9) (Stop loss: 150.5)",
            "===Algo_Trading===$TRADE$CLOSE$SENSEX 18 SEP 81500 CALL$162.4$2025-09-12 14:45:00+05:30$profit_booking$162.4"
        ]
        
        # Mixed concurrent scenario
        self.mixed_concurrent_sequence = [
            "===Algo_Trading===$TRADE$BUY$NIFTY 16 SEP 24800 CALL$165.8$160.2$2025-09-12 14:50:00+05:30",
            "===Algo_Trading===$TRADE$BUY$SENSEX 18 SEP 82000 PUT$148.5$142.1$2025-09-12 14:50:05+05:30",
            "===Algo_Trading===$INTIMATION$Continue to hold trade for $NIFTY 16 SEP 24800 CALL at current price: 168.2 (Entry price: 165.8) (Stop loss: 160.2)",
            "===Algo_Trading===$INTIMATION$Continue to hold trade for $SENSEX 18 SEP 82000 PUT at current price: 151.3 (Entry price: 148.5) (Stop loss: 142.1)",
            "===Algo_Trading===$Update$STOP LOSS to$163.5$for option$NIFTY 16 SEP 24800 CALL current price: 170.8 (Entry price: 165.8)",
            "===Algo_Trading===$Update$STOP LOSS to$145.2$for option$SENSEX 18 SEP 82000 PUT current price: 153.7 (Entry price: 148.5)",
            "===Algo_Trading===$TRADE$CLOSE$NIFTY 16 SEP 24800 CALL$172.5$2025-09-12 15:05:00+05:30$profit_booking$172.5",
            "===Algo_Trading===$TRADE$CLOSE$SENSEX 18 SEP 82000 PUT$155.9$2025-09-12 15:06:00+05:30$profit_booking$155.9"
        ]
    
    def process_signal_sequence(self, sequence: List[str], sequence_name: str, delay_between_signals: float = 1.0) -> Dict[str, Any]:
        """Process a sequence of signals with timing delays"""
        self.logger.info(f"🔄 Processing {sequence_name} sequence ({len(sequence)} signals)...")
        
        sequence_results = []
        start_time = time.time()
        
        for i, signal_text in enumerate(sequence):
            signal_start = time.time()
            
            try:
                self.logger.info(f"📨 Signal {i+1}/{len(sequence)}: {signal_text[:60]}...")
                
                # Parse signal
                parsed_signal = self.signal_handler.parse_message(signal_text)
                
                if parsed_signal:
                    # Process through dataframe manager
                    df_result = self.dataframe_manager.process_signal(parsed_signal)
                    
                    # Process through API handler (only for TRADE actions)
                    api_result = None
                    if parsed_signal.action in ['BUY', 'SELL', 'CLOSE']:
                        api_result = self.api_handler.process_signal(parsed_signal)
                    
                    processing_time = time.time() - signal_start
                    
                    result = {
                        "signal_number": i + 1,
                        "signal_text": signal_text,
                        "parsed": True,
                        "symbol": parsed_signal.symbol,
                        "action": parsed_signal.action,
                        "signal_type": parsed_signal.signal_type,
                        "processing_time": processing_time,
                        "dataframe_processed": df_result is not None,
                        "api_processed": api_result is not None,
                        "api_success": api_result.get('status') == 'success' if api_result else None,
                        "timestamp": datetime.now().isoformat()
                    }
                    
                    # Extract specific details based on signal type
                    if parsed_signal.signal_type == "INTIMATION":
                        result["current_price"] = getattr(parsed_signal, 'current_price', None)
                        result["entry_price"] = getattr(parsed_signal, 'entry_price', None)
                        result["stop_loss"] = getattr(parsed_signal, 'stop_loss', None)
                    elif parsed_signal.signal_type == "UPDATE":
                        result["new_stop_loss"] = getattr(parsed_signal, 'new_stop_loss', None)
                        result["update_type"] = getattr(parsed_signal, 'update_type', None)
                    elif parsed_signal.action in ['BUY', 'CLOSE']:
                        result["price"] = getattr(parsed_signal, 'price', None)
                        result["strike"] = getattr(parsed_signal, 'strike', None)
                        result["option_type"] = getattr(parsed_signal, 'option_type', None)
                    
                    self.logger.info(f"✅ Processed: {parsed_signal.symbol} {parsed_signal.action} ({processing_time:.3f}s)")
                    
                else:
                    result = {
                        "signal_number": i + 1,
                        "signal_text": signal_text,
                        "parsed": False,
                        "error": "Failed to parse signal",
                        "processing_time": time.time() - signal_start,
                        "timestamp": datetime.now().isoformat()
                    }
                    self.logger.error(f"❌ Failed to parse signal {i+1}")
                
                sequence_results.append(result)
                
                # Add delay between signals (except for last signal)
                if i < len(sequence) - 1 and delay_between_signals > 0:
                    time.sleep(delay_between_signals)
                
            except Exception as e:
                result = {
                    "signal_number": i + 1,
                    "signal_text": signal_text,
                    "parsed": False,
                    "error": str(e),
                    "processing_time": time.time() - signal_start,
                    "timestamp": datetime.now().isoformat()
                }
                sequence_results.append(result)
                self.logger.error(f"❌ Exception processing signal {i+1}: {e}")
        
        total_time = time.time() - start_time
        successful_signals = len([r for r in sequence_results if r.get('parsed', False)])
        
        return {
            "sequence_name": sequence_name,
            "total_signals": len(sequence),
            "successful_signals": successful_signals,
            "failed_signals": len(sequence) - successful_signals,
            "total_processing_time": total_time,
            "average_signal_time": total_time / len(sequence) if sequence else 0,
            "success_rate": (successful_signals / len(sequence) * 100) if sequence else 0,
            "signal_results": sequence_results
        }
    
    def test_nifty_put_lifecycle(self) -> Dict[str, Any]:
        """Test the complete NIFTY PUT trade lifecycle"""
        self.logger.info("🎯 Testing NIFTY PUT complete trade lifecycle...")
        return self.process_signal_sequence(self.nifty_put_sequence, "NIFTY_PUT_LIFECYCLE", 0.5)
    
    def test_sensex_call_lifecycle(self) -> Dict[str, Any]:
        """Test SENSEX CALL trade lifecycle"""
        self.logger.info("🎯 Testing SENSEX CALL trade lifecycle...")
        return self.process_signal_sequence(self.sensex_call_sequence, "SENSEX_CALL_LIFECYCLE", 0.5)
    
    def test_mixed_concurrent_trades(self) -> Dict[str, Any]:
        """Test mixed concurrent trades (NIFTY CALL + SENSEX PUT)"""
        self.logger.info("🎯 Testing mixed concurrent trades...")
        return self.process_signal_sequence(self.mixed_concurrent_sequence, "MIXED_CONCURRENT_TRADES", 0.3)
    
    def validate_trade_consistency(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Validate trade consistency and data integrity"""
        self.logger.info("🔍 Validating trade consistency...")
        
        validation_results = {
            "trade_sequences_validated": 0,
            "consistency_issues": [],
            "data_integrity_issues": [],
            "recommendations": []
        }
        
        # Check for proper trade lifecycle
        for sequence_result in results.values():
            if isinstance(sequence_result, dict) and 'signal_results' in sequence_result:
                validation_results["trade_sequences_validated"] += 1
                
                signal_results = sequence_result['signal_results']
                
                # Check for BUY -> INTIMATION/UPDATE -> CLOSE pattern
                actions = [r.get('action') for r in signal_results if r.get('parsed')]
                
                if 'BUY' in actions and 'CLOSE' in actions:
                    buy_index = actions.index('BUY')
                    close_index = actions.index('CLOSE')
                    
                    if close_index <= buy_index:
                        validation_results["consistency_issues"].append(
                            f"CLOSE signal appears before or at same position as BUY in {sequence_result['sequence_name']}"
                        )
                    
                    # Check for INTIMATION/UPDATE signals between BUY and CLOSE
                    middle_actions = actions[buy_index+1:close_index]
                    if not any(action in ['HOLD', 'UPDATE'] for action in middle_actions):
                        validation_results["data_integrity_issues"].append(
                            f"No INTIMATION/UPDATE signals between BUY and CLOSE in {sequence_result['sequence_name']}"
                        )
                
                # Check for failed signals
                failed_signals = [r for r in signal_results if not r.get('parsed', False)]
                if failed_signals:
                    validation_results["consistency_issues"].append(
                        f"{len(failed_signals)} failed signals in {sequence_result['sequence_name']}"
                    )
        
        # Generate recommendations
        if not validation_results["consistency_issues"] and not validation_results["data_integrity_issues"]:
            validation_results["recommendations"].append("✅ All trade sequences processed correctly with proper lifecycle")
        else:
            validation_results["recommendations"].append("⚠️ Some consistency issues found - review signal processing logic")
        
        return validation_results

    def run_all_sequential_tests(self) -> Dict[str, Any]:
        """Run all sequential trading tests"""
        self.logger.info("🚀 Starting comprehensive sequential trading tests...")

        test_results = {
            "test_start_time": datetime.now().isoformat(),
            "tests": {}
        }

        # Test 1: NIFTY PUT complete lifecycle (your provided sequence)
        test_results["tests"]["nifty_put_lifecycle"] = self.test_nifty_put_lifecycle()
        time.sleep(2)  # Brief pause between test sequences

        # Test 2: SENSEX CALL lifecycle
        test_results["tests"]["sensex_call_lifecycle"] = self.test_sensex_call_lifecycle()
        time.sleep(2)

        # Test 3: Mixed concurrent trades
        test_results["tests"]["mixed_concurrent_trades"] = self.test_mixed_concurrent_trades()

        test_results["test_end_time"] = datetime.now().isoformat()

        # Validate trade consistency
        test_results["validation"] = self.validate_trade_consistency(test_results["tests"])

        # Generate comprehensive summary
        test_results["summary"] = self.generate_comprehensive_summary(test_results)

        return test_results

    def generate_comprehensive_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive summary of all tests"""
        summary = {
            "total_sequences_tested": 0,
            "total_signals_processed": 0,
            "total_successful_signals": 0,
            "overall_success_rate": 0,
            "sequence_performance": {},
            "signal_type_analysis": {},
            "critical_issues": [],
            "performance_metrics": {},
            "recommendations": []
        }

        # Analyze each test sequence
        for test_name, test_result in results["tests"].items():
            if isinstance(test_result, dict):
                summary["total_sequences_tested"] += 1
                summary["total_signals_processed"] += test_result.get("total_signals", 0)
                summary["total_successful_signals"] += test_result.get("successful_signals", 0)

                # Store sequence performance
                summary["sequence_performance"][test_name] = {
                    "success_rate": test_result.get("success_rate", 0),
                    "total_time": test_result.get("total_processing_time", 0),
                    "avg_signal_time": test_result.get("average_signal_time", 0)
                }

                # Analyze signal types
                if "signal_results" in test_result:
                    for signal_result in test_result["signal_results"]:
                        if signal_result.get("parsed"):
                            action = signal_result.get("action", "UNKNOWN")
                            if action not in summary["signal_type_analysis"]:
                                summary["signal_type_analysis"][action] = {"count": 0, "success": 0}
                            summary["signal_type_analysis"][action]["count"] += 1
                            summary["signal_type_analysis"][action]["success"] += 1

        # Calculate overall success rate
        if summary["total_signals_processed"] > 0:
            summary["overall_success_rate"] = (summary["total_successful_signals"] / summary["total_signals_processed"]) * 100

        # Performance metrics
        total_time = sum(perf["total_time"] for perf in summary["sequence_performance"].values())
        summary["performance_metrics"] = {
            "total_execution_time": total_time,
            "signals_per_second": summary["total_signals_processed"] / total_time if total_time > 0 else 0,
            "average_sequence_time": total_time / summary["total_sequences_tested"] if summary["total_sequences_tested"] > 0 else 0
        }

        # Add validation issues to critical issues
        if "validation" in results:
            validation = results["validation"]
            summary["critical_issues"].extend(validation.get("consistency_issues", []))
            summary["critical_issues"].extend(validation.get("data_integrity_issues", []))

        # Generate recommendations
        if summary["overall_success_rate"] >= 95:
            summary["recommendations"].append("✅ Excellent performance! System handles sequential signals very well.")
        elif summary["overall_success_rate"] >= 85:
            summary["recommendations"].append("✅ Good performance with minor issues to address.")
        else:
            summary["recommendations"].append("❌ Performance issues detected. Review signal processing logic.")

        if summary["performance_metrics"]["signals_per_second"] > 5:
            summary["recommendations"].append("⚡ High throughput achieved - system can handle rapid signal sequences.")
        elif summary["performance_metrics"]["signals_per_second"] < 1:
            summary["recommendations"].append("🐌 Low throughput detected - consider performance optimization.")

        if len(summary["critical_issues"]) == 0:
            summary["recommendations"].append("🔒 No critical issues found - system is stable and reliable.")
        else:
            summary["recommendations"].append("⚠️ Critical issues found - immediate attention required.")

        return summary

    def export_detailed_report(self, results: Dict[str, Any], filename: str = None):
        """Export detailed test report"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"tests/sequential_test_report_{timestamp}.json"

        # Create a detailed report
        report = {
            "test_metadata": {
                "test_type": "Sequential Trading Signal Processing",
                "test_date": datetime.now().isoformat(),
                "total_sequences": results["summary"]["total_sequences_tested"],
                "total_signals": results["summary"]["total_signals_processed"]
            },
            "executive_summary": {
                "overall_success_rate": f"{results['summary']['overall_success_rate']:.1f}%",
                "performance_rating": "EXCELLENT" if results["summary"]["overall_success_rate"] >= 95 else
                                    "GOOD" if results["summary"]["overall_success_rate"] >= 85 else "NEEDS_IMPROVEMENT",
                "critical_issues_count": len(results["summary"]["critical_issues"]),
                "recommendations_count": len(results["summary"]["recommendations"])
            },
            "detailed_results": results,
            "signal_breakdown": {
                "nifty_put_sequence_signals": len(self.nifty_put_sequence),
                "sensex_call_sequence_signals": len(self.sensex_call_sequence),
                "mixed_concurrent_sequence_signals": len(self.mixed_concurrent_sequence)
            }
        }

        # Save report
        os.makedirs(os.path.dirname(filename), exist_ok=True)
        with open(filename, 'w') as f:
            json.dump(report, f, indent=2, default=str)

        self.logger.info(f"📄 Detailed report exported to: {filename}")
        return filename


def main():
    """Main execution function"""
    print("🎯 Starting Sequential Trading Signal Test Suite")
    print("=" * 60)
    print("Testing complete trade lifecycles with realistic signal sequences")
    print("Including INTIMATION, UPDATE, and CLOSE signals from actual trading")
    print("=" * 60)

    # Create test instance
    test_suite = SequentialTradingTest()

    # Run all sequential tests
    results = test_suite.run_all_sequential_tests()

    # Print executive summary
    summary = results["summary"]
    print(f"\n📊 SEQUENTIAL TRADING TEST SUMMARY")
    print(f"{'='*50}")
    print(f"Total Sequences Tested: {summary['total_sequences_tested']}")
    print(f"Total Signals Processed: {summary['total_signals_processed']}")
    print(f"Successful Signals: {summary['total_successful_signals']}")
    print(f"Overall Success Rate: {summary['overall_success_rate']:.1f}%")
    print(f"Total Execution Time: {summary['performance_metrics']['total_execution_time']:.2f}s")
    print(f"Signals Per Second: {summary['performance_metrics']['signals_per_second']:.2f}")

    # Print sequence performance
    print(f"\n📈 SEQUENCE PERFORMANCE:")
    for seq_name, perf in summary["sequence_performance"].items():
        print(f"  {seq_name.upper()}:")
        print(f"    Success Rate: {perf['success_rate']:.1f}%")
        print(f"    Avg Signal Time: {perf['avg_signal_time']:.3f}s")

    # Print signal type analysis
    print(f"\n🔍 SIGNAL TYPE ANALYSIS:")
    for signal_type, analysis in summary["signal_type_analysis"].items():
        success_rate = (analysis['success'] / analysis['count'] * 100) if analysis['count'] > 0 else 0
        print(f"  {signal_type}: {analysis['count']} signals, {success_rate:.1f}% success")

    # Print critical issues
    if summary["critical_issues"]:
        print(f"\n❌ CRITICAL ISSUES:")
        for issue in summary["critical_issues"]:
            print(f"  • {issue}")

    # Print recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    for rec in summary["recommendations"]:
        print(f"  • {rec}")

    # Export detailed report
    report_file = test_suite.export_detailed_report(results)

    print(f"\n📄 Detailed report saved to: {report_file}")
    print("🏁 Sequential trading tests completed!")

    # Return results for potential further analysis
    return results


if __name__ == "__main__":
    main()
