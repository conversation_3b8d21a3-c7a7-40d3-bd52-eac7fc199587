import os
import requests
import pandas as pd
from datetime import datetime, time, timedelta
from typing import Dict, Optional, Any, List
import pytz
from .logger import logger
from .config import config
from .api_handler import TradeSignal


class MarketDataManager:
    """
    Enhanced market data manager with real-time LTP fetching, 
    historical data retrieval, and market hours detection
    """
    
    def __init__(self, smart_api=None):
        self.logger = logger.get_logger('market_data')
        self.smart_api = smart_api
        self.ist_timezone = pytz.timezone('Asia/Kolkata')
        
        # Market hours configuration
        self.market_start_time = time(9, 15)  # 9:15 AM
        self.market_end_time = time(15, 30)   # 3:30 PM
        
        # Market holidays (can be expanded or loaded from external source)
        self.market_holidays = [
            # Add major market holidays here
            # Format: datetime(year, month, day)
        ]
        
        self.logger.info("Market Data Manager initialized")
    
    def is_market_open(self) -> bool:
        """
        Enhanced market hours detection with timezone and holiday support
        """
        try:
            # Get current time in IST
            now_ist = datetime.now(self.ist_timezone)
            current_time = now_ist.time()
            current_date = now_ist.date()
            
            # Check if it's a weekend
            if now_ist.weekday() >= 5:  # Saturday = 5, Sunday = 6
                return False
            
            # Check if it's a market holiday
            if datetime.combine(current_date, time()) in self.market_holidays:
                return False
            
            # Check if current time is within market hours
            return self.market_start_time <= current_time <= self.market_end_time
            
        except Exception as e:
            self.logger.error(f"Error checking market hours: {e}")
            return False
    
    def get_market_status(self) -> Dict[str, Any]:
        """
        Get detailed market status information
        """
        try:
            now_ist = datetime.now(self.ist_timezone)
            is_open = self.is_market_open()
            
            status = {
                "is_open": is_open,
                "current_time_ist": now_ist.strftime("%Y-%m-%d %H:%M:%S %Z"),
                "market_start": self.market_start_time.strftime("%H:%M"),
                "market_end": self.market_end_time.strftime("%H:%M"),
                "is_weekend": now_ist.weekday() >= 5,
                "weekday": now_ist.strftime("%A")
            }
            
            if is_open:
                # Calculate time until market close
                market_close = datetime.combine(now_ist.date(), self.market_end_time)
                market_close = self.ist_timezone.localize(market_close)
                time_to_close = market_close - now_ist
                status["time_to_close"] = str(time_to_close).split('.')[0]  # Remove microseconds
            else:
                # Calculate time until next market open
                next_open = self._get_next_market_open(now_ist)
                if next_open:
                    time_to_open = next_open - now_ist
                    status["time_to_next_open"] = str(time_to_open).split('.')[0]
            
            return status
            
        except Exception as e:
            self.logger.error(f"Error getting market status: {e}")
            return {"is_open": False, "error": str(e)}
    
    def _get_next_market_open(self, current_time) -> Optional[datetime]:
        """
        Calculate the next market opening time
        """
        try:
            next_day = current_time.date() + timedelta(days=1)
            
            # Find next weekday
            while next_day.weekday() >= 5:  # Skip weekends
                next_day += timedelta(days=1)
            
            # Create next market open datetime
            next_open = datetime.combine(next_day, self.market_start_time)
            next_open = self.ist_timezone.localize(next_open)
            
            return next_open
            
        except Exception as e:
            self.logger.error(f"Error calculating next market open: {e}")
            return None
    
    def get_live_price(self, instrument: Dict) -> Optional[float]:
        """
        Get real-time LTP for an instrument during market hours
        """
        try:
            if not self.smart_api:
                self.logger.error("SmartAPI not initialized")
                return None
            
            if not self.is_market_open():
                self.logger.warning("Market is closed, cannot fetch live price")
                return None
            
            data = self.smart_api.ltpData(
                exchange=instrument['exchange'],
                tradingsymbol=instrument['tradingsymbol'],
                symboltoken=instrument['token']
            )
            
            if data and data.get('status') and data.get('data'):
                ltp = data['data'].get('ltp')
                if ltp:
                    self.logger.info(f"Live price for {instrument['custom_symbol']}: {ltp}")
                    return float(ltp)
            
            self.logger.warning(f"No live price data available for {instrument['custom_symbol']}")
            return None
            
        except Exception as e:
            self.logger.error(f"Error fetching live price: {e}")
            return None
    
    def get_historical_price(self, instrument: Dict, date: datetime = None) -> Optional[float]:
        """
        Get historical closing price for an instrument (for after-hours) with multiple fallback attempts
        """
        try:
            if not self.smart_api:
                self.logger.error("SmartAPI not initialized")
                return None

            # Try multiple dates if no specific date provided
            dates_to_try = []
            if date:
                dates_to_try = [date]
            else:
                # Try last 5 trading days
                for i in range(1, 6):
                    dates_to_try.append(self._get_previous_trading_day(days_back=i))

            for attempt_date in dates_to_try:
                try:
                    # Format date for API call
                    from_date = attempt_date.strftime("%Y-%m-%d 09:15")
                    to_date = attempt_date.strftime("%Y-%m-%d 15:30")

                    self.logger.info(f"Attempting to fetch historical data for {instrument.get('custom_symbol', instrument.get('tradingsymbol'))} on {attempt_date.strftime('%Y-%m-%d')}")

                    # Get historical data
                    hist_data = self.smart_api.getCandleData({
                        "exchange": instrument['exchange'],
                        "symboltoken": instrument['token'],
                        "interval": "ONE_DAY",
                        "fromdate": from_date,
                        "todate": to_date
                    })

                    if hist_data and hist_data.get('status') and hist_data.get('data'):
                        data_points = hist_data['data']
                        if data_points:
                            # Get closing price (index 4 in OHLCV format)
                            closing_price = data_points[-1][4]  # Last data point, closing price
                            self.logger.info(f"Historical closing price for {instrument.get('custom_symbol', instrument.get('tradingsymbol'))}: {closing_price} on {attempt_date.strftime('%Y-%m-%d')}")
                            return float(closing_price)

                    self.logger.debug(f"No data for {attempt_date.strftime('%Y-%m-%d')}, trying next date")

                except Exception as date_error:
                    self.logger.debug(f"Error fetching data for {attempt_date.strftime('%Y-%m-%d')}: {date_error}")
                    continue

            self.logger.warning(f"No historical price data available for {instrument.get('custom_symbol', instrument.get('tradingsymbol'))} after trying multiple dates")
            return None

        except Exception as e:
            self.logger.error(f"Error fetching historical price: {e}")
            return None
    
    def _get_previous_trading_day(self) -> datetime:
        """
        Get the previous trading day (excluding weekends and holidays)
        """
        try:
            current_date = datetime.now(self.ist_timezone).date()
            prev_date = current_date - timedelta(days=1)
            
            # Skip weekends
            while prev_date.weekday() >= 5:
                prev_date -= timedelta(days=1)
            
            # Convert to datetime for API compatibility
            return datetime.combine(prev_date, time(15, 30))  # Use market close time
            
        except Exception as e:
            self.logger.error(f"Error getting previous trading day: {e}")
            return datetime.now() - timedelta(days=1)
    
    def get_current_market_price(self, instrument: Dict) -> Optional[float]:
        """
        Get current market price - live if market open, historical if closed
        """
        try:
            if self.is_market_open():
                # Market is open, get live price
                price = self.get_live_price(instrument)
                if price:
                    return price
                else:
                    self.logger.warning("Failed to get live price, falling back to historical")
            
            # Market is closed or live price failed, get historical price
            return self.get_historical_price(instrument)
            
        except Exception as e:
            self.logger.error(f"Error getting current market price: {e}")
            return None
    
    def get_multiple_prices(self, instruments: List[Dict]) -> Dict[str, Optional[float]]:
        """
        Get current market prices for multiple instruments efficiently
        """
        try:
            prices = {}
            
            for instrument in instruments:
                instrument_key = instrument.get('custom_symbol', instrument.get('tradingsymbol', 'unknown'))
                price = self.get_current_market_price(instrument)
                prices[instrument_key] = price
            
            return prices
            
        except Exception as e:
            self.logger.error(f"Error getting multiple prices: {e}")
            return {}
