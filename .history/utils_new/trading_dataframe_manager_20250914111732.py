import pandas as pd
import os
from datetime import datetime
from typing import Dict, Optional, Any, List
from .logger import logger
from .api_handler import TradeSignal
from .market_data_manager import MarketDataManager
from .instrument_updater import InstrumentUpdater


class TradingDataframeManager:
    """
    Global trading dataframe management system that maintains one active dataframe 
    per trading instrument, where each dataframe tracks a single trade lifecycle 
    from entry to exit.
    """
    
    def __init__(self, smart_api=None, api_handler=None):
        self.logger = logger.get_logger('trading_dataframes')
        # Dictionary to store active dataframes: {instrument_name: DataFrame}
        self.active_dataframes: Dict[str, pd.DataFrame] = {}
        # Directory for CSV log files
        self.log_directory = "trade_logs"
        self._ensure_log_directory()

        # Initialize market data manager and instrument updater
        self.market_data_manager = MarketDataManager(smart_api)
        self.instrument_updater = InstrumentUpdater(smart_api)
        self.api_handler = api_handler  # Reference to API handler for instrument lookup

        # Auto-update instruments on initialization
        self._auto_update_instruments()

        self.logger.info("Trading Dataframe Manager initialized with market data integration")

    def _auto_update_instruments(self):
        """Automatically update instrument files if needed"""
        try:
            update_success = self.instrument_updater.update_instruments()
            if update_success:
                self.logger.info("Instrument files updated successfully")
            else:
                self.logger.warning("Instrument update failed, using existing files")
        except Exception as e:
            self.logger.error(f"Error during auto instrument update: {e}")

    def _ensure_log_directory(self):
        """Ensure the log directory exists"""
        if not os.path.exists(self.log_directory):
            os.makedirs(self.log_directory)
            self.logger.info(f"Created log directory: {self.log_directory}")

    def _get_daily_csv_filename(self) -> str:
        """Generate daily CSV filename in format YYYY-MM-DD_Trade_Log.csv"""
        today = datetime.now().strftime("%Y-%m-%d")
        return os.path.join(self.log_directory, f"{today}_Trade_Log.csv")

    def _log_completed_trade_to_csv(self, df: pd.DataFrame, instrument_name: str):
        """
        Append completed trade to daily CSV log file
        """
        try:
            csv_filename = self._get_daily_csv_filename()

            # Check if CSV file exists
            file_exists = os.path.exists(csv_filename)

            # Write to CSV (append mode if file exists, create if not)
            df.to_csv(csv_filename, mode='a', header=not file_exists, index=False)

            self.logger.info(f"Logged completed trade for {instrument_name} to {csv_filename}")

        except Exception as e:
            self.logger.error(f"Failed to log trade to CSV: {e}")

    def _create_empty_dataframe(self) -> pd.DataFrame:
        """Create an empty dataframe with the required structure"""
        columns = [
            'Instrument_name',           # Complete option chain name
            'trade_entry_time',          # Timestamp when trade signal was received
            'trade_exit_time',           # Timestamp when close signal was received (initially null)
            'Trade_entry_price_sig',     # Entry price from trading signal
            'Trade_exit_price_sig',      # Exit price from trading signal (initially null)
            'profit_loss_sig',           # Calculated P&L based on signal prices (initially null)
            'Trade_entry_price_act',     # Actual executed entry price (placeholder)
            'Trade_exit_price_act',      # Actual executed exit price (placeholder)
            'profit_loss_act'            # Actual P&L from executed trades (placeholder)
        ]
        
        return pd.DataFrame(columns=columns)
    
    def _get_instrument_name(self, signal: TradeSignal) -> str:
        """
        Generate standardized instrument name from signal
        Examples: 
        - "NIFTY24DEC21000CE" 
        - "NIFTY 16 SEP 25000 CALL"
        - "SENSEX 18 SEP 81500 PUT"
        """
        if signal.strike and signal.option_type and signal.expiry_date:
            # Option instrument
            option_type_full = "CALL" if signal.option_type == "CE" else "PUT"
            return f"{signal.symbol} {signal.expiry_date} {int(signal.strike)} {option_type_full}"
        elif signal.strike and signal.option_type:
            # Option without expiry date
            option_type_full = "CALL" if signal.option_type == "CE" else "PUT"
            return f"{signal.symbol}{int(signal.strike)}{signal.option_type}"
        else:
            # Stock/Index instrument
            return signal.symbol

    def _get_actual_market_price(self, signal: TradeSignal) -> Optional[float]:
        """
        Get actual market price for an instrument using market data manager
        """
        try:
            if not self.api_handler:
                self.logger.warning("API handler not available for market price lookup")
                return None

            # Find instrument details
            instrument = self.api_handler.find_instrument(signal)
            if not instrument:
                self.logger.warning(f"Instrument not found for {signal.symbol}")
                return None

            # Get current market price (live or historical based on market hours)
            market_price = self.market_data_manager.get_current_market_price(instrument)

            if market_price:
                market_status = "live" if self.market_data_manager.is_market_open() else "historical"
                self.logger.info(f"Retrieved {market_status} market price for {instrument['custom_symbol']}: {market_price}")
                return market_price
            else:
                self.logger.warning(f"Could not retrieve market price for {instrument['custom_symbol']}")
                return None

        except Exception as e:
            self.logger.error(f"Error getting actual market price: {e}")
            return None

    def create_or_update_trade_entry(self, signal: TradeSignal) -> Dict[str, Any]:
        """
        Create new dataframe or update existing one for trade entry (BUY/SELL signals)
        """
        try:
            instrument_name = self._get_instrument_name(signal)
            
            # Check if there's already an active trade for this instrument
            if instrument_name in self.active_dataframes:
                self.logger.warning(f"Active trade already exists for {instrument_name}. Overwriting with new entry.")
            
            # Create new dataframe for this trade
            df = self._create_empty_dataframe()
            
            # Get actual market price for entry (optional)
            actual_entry_price = self._get_actual_market_price(signal)

            # Add the trade entry data
            trade_data = {
                'Instrument_name': instrument_name,
                'trade_entry_time': signal.timestamp or datetime.now(),
                'trade_exit_time': None,
                'Trade_entry_price_sig': signal.price or signal.entry_price,
                'Trade_exit_price_sig': None,
                'profit_loss_sig': None,
                'Trade_entry_price_act': actual_entry_price,  # Actual market price at entry
                'Trade_exit_price_act': None,   # Will be populated at exit
                'profit_loss_act': None        # Will be calculated at exit
            }
            
            # Add row to dataframe
            df.loc[0] = trade_data
            
            # Store in active dataframes
            self.active_dataframes[instrument_name] = df
            
            self.logger.info(f"Created new trade entry for {instrument_name} at price {trade_data['Trade_entry_price_sig']}")
            
            return {
                "status": "success",
                "message": f"Trade entry created for {instrument_name}",
                "instrument_name": instrument_name,
                "entry_price": trade_data['Trade_entry_price_sig'],
                "entry_time": trade_data['trade_entry_time']
            }
            
        except Exception as e:
            error_msg = f"Failed to create trade entry: {str(e)}"
            self.logger.error(error_msg)
            return {"status": "error", "message": error_msg}
    
    def update_trade_exit(self, signal: TradeSignal) -> Dict[str, Any]:
        """
        Update existing dataframe with trade exit information (CLOSE signals)
        """
        try:
            instrument_name = self._get_instrument_name(signal)
            
            # Check if there's an active trade for this instrument
            if instrument_name not in self.active_dataframes:
                error_msg = f"No active trade found for {instrument_name} to close"
                self.logger.error(error_msg)
                return {"status": "error", "message": error_msg}
            
            df = self.active_dataframes[instrument_name]
            
            # Update the exit information
            df.loc[0, 'trade_exit_time'] = signal.timestamp or datetime.now()
            df.loc[0, 'Trade_exit_price_sig'] = signal.price or signal.exit_price

            # Get actual market price for exit
            actual_exit_price = self._get_actual_market_price(signal)
            df.loc[0, 'Trade_exit_price_act'] = actual_exit_price

            # Calculate P&L based on signal prices
            entry_price_sig = df.loc[0, 'Trade_entry_price_sig']
            exit_price_sig = df.loc[0, 'Trade_exit_price_sig']

            if entry_price_sig is not None and exit_price_sig is not None:
                # Assuming BUY entry (profit = exit - entry), adjust logic as needed
                profit_loss_sig = float(exit_price_sig) - float(entry_price_sig)
                df.loc[0, 'profit_loss_sig'] = profit_loss_sig

            # Calculate P&L based on actual market prices
            entry_price_act = df.loc[0, 'Trade_entry_price_act']
            exit_price_act = df.loc[0, 'Trade_exit_price_act']

            if entry_price_act is not None and exit_price_act is not None:
                profit_loss_act = float(exit_price_act) - float(entry_price_act)
                df.loc[0, 'profit_loss_act'] = profit_loss_act
                self.logger.info(f"Actual P&L calculated: {profit_loss_act} (Entry: {entry_price_act}, Exit: {exit_price_act})")
            else:
                self.logger.warning("Could not calculate actual P&L due to missing market prices")
            
            self.logger.info(f"Updated trade exit for {instrument_name} at signal price {exit_price_sig}, actual price {actual_exit_price}")

            # Log completed trade to CSV
            self._log_completed_trade_to_csv(df, instrument_name)

            # Prepare return data before cleanup
            result_data = {
                "status": "success",
                "message": f"Trade completed and logged for {instrument_name}",
                "instrument_name": instrument_name,
                "exit_price_signal": exit_price_sig,
                "exit_price_actual": actual_exit_price,
                "exit_time": df.loc[0, 'trade_exit_time'],
                "profit_loss_signal": df.loc[0, 'profit_loss_sig'],
                "profit_loss_actual": df.loc[0, 'profit_loss_act']
            }

            # Clean up: Remove completed trade from active dataframes
            self._cleanup_completed_trade(instrument_name)

            return result_data
            
        except Exception as e:
            error_msg = f"Failed to update trade exit: {str(e)}"
            self.logger.error(error_msg)
            return {"status": "error", "message": error_msg}

    def handle_intimation_signal(self, signal: TradeSignal) -> Dict[str, Any]:
        """
        Handle INTIMATION signals (hold messages) - no dataframe updates needed
        """
        try:
            instrument_name = self._get_instrument_name(signal)

            # Check if there's an active trade for this instrument
            if instrument_name not in self.active_dataframes:
                self.logger.warning(f"Received intimation for {instrument_name} but no active trade found")
                return {
                    "status": "warning",
                    "message": f"Intimation received for {instrument_name} but no active trade found"
                }

            self.logger.info(f"Intimation received for {instrument_name} - continuing to hold")

            return {
                "status": "success",
                "message": f"Intimation processed for {instrument_name}",
                "instrument_name": instrument_name,
                "current_price": signal.current_price,
                "entry_price": signal.entry_price,
                "stop_loss": signal.stop_loss
            }

        except Exception as e:
            error_msg = f"Failed to handle intimation signal: {str(e)}"
            self.logger.error(error_msg)
            return {"status": "error", "message": error_msg}

    def handle_update_signal(self, signal: TradeSignal) -> Dict[str, Any]:
        """
        Handle UPDATE signals (stop loss updates) - no dataframe updates needed for now
        """
        try:
            instrument_name = self._get_instrument_name(signal)

            # Check if there's an active trade for this instrument
            if instrument_name not in self.active_dataframes:
                self.logger.warning(f"Received update for {instrument_name} but no active trade found")
                return {
                    "status": "warning",
                    "message": f"Update received for {instrument_name} but no active trade found"
                }

            self.logger.info(f"Update received for {instrument_name}: {signal.update_type} to {signal.new_stop_loss}")

            return {
                "status": "success",
                "message": f"Update processed for {instrument_name}",
                "instrument_name": instrument_name,
                "update_type": signal.update_type,
                "new_value": signal.new_stop_loss,
                "current_price": signal.current_price
            }

        except Exception as e:
            error_msg = f"Failed to handle update signal: {str(e)}"
            self.logger.error(error_msg)
            return {"status": "error", "message": error_msg}

    def get_active_trade(self, instrument_name: str) -> Optional[pd.DataFrame]:
        """
        Get the active dataframe for a specific instrument
        """
        return self.active_dataframes.get(instrument_name)

    def get_all_active_trades(self) -> Dict[str, pd.DataFrame]:
        """
        Get all active trading dataframes
        """
        return self.active_dataframes.copy()

    def _cleanup_completed_trade(self, instrument_name: str):
        """
        Internal method to clean up completed trade from active dataframes
        """
        try:
            if instrument_name in self.active_dataframes:
                del self.active_dataframes[instrument_name]
                self.logger.info(f"Cleaned up completed trade for {instrument_name} from active memory")
            else:
                self.logger.warning(f"No active trade found for {instrument_name} to clean up")
        except Exception as e:
            self.logger.error(f"Failed to cleanup trade for {instrument_name}: {e}")

    def remove_completed_trade(self, instrument_name: str) -> bool:
        """
        Manually remove a completed trade from active dataframes (for external use)
        """
        try:
            if instrument_name in self.active_dataframes:
                del self.active_dataframes[instrument_name]
                self.logger.info(f"Manually removed trade for {instrument_name}")
                return True
            else:
                self.logger.warning(f"No active trade found for {instrument_name} to remove")
                return False
        except Exception as e:
            self.logger.error(f"Failed to remove trade for {instrument_name}: {e}")
            return False

    def get_trade_summary(self) -> Dict[str, Any]:
        """
        Get summary of all active trades
        """
        try:
            summary = {
                "total_active_trades": len(self.active_dataframes),
                "instruments": list(self.active_dataframes.keys()),
                "trades_detail": {}
            }

            for instrument_name, df in self.active_dataframes.items():
                if not df.empty:
                    trade_info = {
                        "entry_time": df.loc[0, 'trade_entry_time'],
                        "entry_price": df.loc[0, 'Trade_entry_price_sig'],
                        "exit_time": df.loc[0, 'trade_exit_time'],
                        "exit_price": df.loc[0, 'Trade_exit_price_sig'],
                        "profit_loss": df.loc[0, 'profit_loss_sig'],
                        "is_closed": df.loc[0, 'trade_exit_time'] is not None
                    }
                    summary["trades_detail"][instrument_name] = trade_info

            return summary

        except Exception as e:
            self.logger.error(f"Failed to generate trade summary: {e}")
            return {"error": str(e)}

    def get_daily_csv_path(self) -> str:
        """
        Get the path to today's CSV log file
        """
        return self._get_daily_csv_filename()

    def get_csv_trade_count(self) -> int:
        """
        Get the number of completed trades logged in today's CSV file
        """
        try:
            csv_path = self._get_daily_csv_filename()
            if os.path.exists(csv_path):
                df = pd.read_csv(csv_path)
                return len(df)
            return 0
        except Exception as e:
            self.logger.error(f"Failed to count CSV trades: {e}")
            return 0

    def get_market_status(self) -> Dict[str, Any]:
        """
        Get current market status information
        """
        return self.market_data_manager.get_market_status()

    def update_all_active_prices(self) -> Dict[str, Any]:
        """
        Update current market prices for all active trades
        """
        try:
            if not self.active_dataframes:
                return {"status": "success", "message": "No active trades to update"}

            updated_count = 0
            failed_count = 0

            for instrument_name, df in self.active_dataframes.items():
                try:
                    # Create a temporary signal to get instrument details
                    # This is a workaround - in practice, we might want to store instrument details
                    # Parse instrument name back to signal components
                    parts = instrument_name.split()
                    if len(parts) >= 4:
                        symbol = parts[0]
                        expiry_date = f"{parts[1]} {parts[2]}"
                        strike = float(parts[3])
                        option_type = "CE" if "CALL" in parts[4] else "PE"

                        temp_signal = TradeSignal(
                            symbol=symbol,
                            action="TEMP",
                            strike=strike,
                            option_type=option_type,
                            expiry_date=expiry_date,
                            signal_type="TEMP"
                        )

                        current_price = self._get_actual_market_price(temp_signal)
                        if current_price:
                            # Store current price in a new column for reference
                            df.loc[0, 'current_market_price'] = current_price
                            df.loc[0, 'price_update_time'] = datetime.now()
                            updated_count += 1
                        else:
                            failed_count += 1
                    else:
                        failed_count += 1

                except Exception as e:
                    self.logger.error(f"Error updating price for {instrument_name}: {e}")
                    failed_count += 1

            return {
                "status": "success",
                "message": f"Price update completed: {updated_count} updated, {failed_count} failed",
                "updated_count": updated_count,
                "failed_count": failed_count
            }

        except Exception as e:
            error_msg = f"Error updating active prices: {str(e)}"
            self.logger.error(error_msg)
            return {"status": "error", "message": error_msg}

    def force_instrument_update(self) -> bool:
        """
        Force update of instrument files
        """
        try:
            return self.instrument_updater.update_instruments(force_update=True)
        except Exception as e:
            self.logger.error(f"Error forcing instrument update: {e}")
            return False

    def get_instrument_update_status(self) -> Dict[str, Any]:
        """
        Get instrument update status
        """
        try:
            return self.instrument_updater.get_update_status()
        except Exception as e:
            self.logger.error(f"Error getting instrument update status: {e}")
            return {"error": str(e)}

    def process_signal(self, signal: TradeSignal) -> Dict[str, Any]:
        """
        Main method to process any type of trading signal and update dataframes accordingly
        """
        try:
            self.logger.info(f"Processing signal: {signal.signal_type} - {signal.action} for {signal.symbol}")

            if signal.signal_type == "TRADE":
                if signal.action.upper() in ["BUY", "SELL"]:
                    # Entry signal
                    return self.create_or_update_trade_entry(signal)
                elif signal.action.upper() == "CLOSE":
                    # Exit signal
                    return self.update_trade_exit(signal)
                else:
                    return {"status": "error", "message": f"Unknown trade action: {signal.action}"}

            elif signal.signal_type == "INTIMATION":
                # Hold signal
                return self.handle_intimation_signal(signal)

            elif signal.signal_type == "UPDATE":
                # Update signal (stop loss, etc.)
                return self.handle_update_signal(signal)

            else:
                return {"status": "error", "message": f"Unknown signal type: {signal.signal_type}"}

        except Exception as e:
            error_msg = f"Failed to process signal: {str(e)}"
            self.logger.error(error_msg)
            return {"status": "error", "message": error_msg}
