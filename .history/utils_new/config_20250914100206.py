import os
from dataclasses import dataclass
from typing import Optional
from datetime import datetime

@dataclass
class APIConfig:
    # Telegram Config
    API_ID: str
    API_HASH: str
    SESSION_NAME: str
    GROUP_NAME: str
    
    # SmartAPI Config
    SMARTAPI_API_KEY: str
    SMARTAPI_USER_ID: str
    SMAR<PERSON>PI_PIN: str
    SMARTAPI_TOTP_SECRET: str
    SMARTAPI_RATE_LIMIT_DELAY: int = 3
    SMARTAPI_MAX_RECONNECT_ATTEMPTS: int = 3
    SMARTAPI_AUTH_DELAY: int = 15
    
    # Trading Settings
    TRADING_START_TIME: str = "09:15"
    TRADING_END_TIME: str = "15:30"
    
    # Feature Flags
    DISABLE_WEBSOCKET: bool = False
    ENABLE_LTP_FETCHING: bool = True
    ENABLE_DEPTH_DATA: bool = True

    # File Paths
    INSTRUMENT_FILE_PATH: str = "Dependencies"
    LOG_DIR: str = "logs"

    @classmethod
    def from_env(cls):
        """Load configuration from environment variables with defaults"""
        return cls(
            # Required configs - using existing values from old config
            API_ID="25551963",
            API_HASH="9f4e9e1abbddf507689ad1eebb6b9ee0",
            SESSION_NAME="telegram_bot_session",
            GROUP_NAME="ಆತ್ಮ ನಿರ್ಭರ",
            SMARTAPI_API_KEY="GvbeXvJX",
            SMARTAPI_USER_ID="s60593162",
            SMARTAPI_PIN="2010",
            SMARTAPI_TOTP_SECRET="EEAZPGYT7COLYB32WAOEO7MLMY",
        )
    
    def validate(self) -> bool:
        """Validate required configuration values"""
        required_fields = [
            'API_ID', 'API_HASH', 'GROUP_NAME',
            'SMARTAPI_API_KEY', 'SMARTAPI_USER_ID',
            'SMARTAPI_PIN', 'SMARTAPI_TOTP_SECRET'
        ]
        
        for field in required_fields:
            if not getattr(self, field):
                return False
        return True

    def is_market_open(self) -> bool:
        """Check if market is currently open"""
        now = datetime.now().time()
        start = datetime.strptime(self.TRADING_START_TIME, "%H:%M").time()
        end = datetime.strptime(self.TRADING_END_TIME, "%H:%M").time()
        return start <= now <= end

config = APIConfig.from_env()
