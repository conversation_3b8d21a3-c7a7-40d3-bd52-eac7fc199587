from typing import Optional, Dict, Any, List
from dataclasses import dataclass
from datetime import datetime
import pandas as pd
import glob
import os
from SmartApi import SmartConnect
import pyotp
from .config import config
from .logger import logger

@dataclass
class TradeSignal:
    symbol: str
    action: str
    strike: Optional[float] = None
    option_type: Optional[str] = None
    timestamp: datetime = datetime.now()
    quantity: int = 1
    price: Optional[float] = None
    stop_loss: Optional[float] = None
    entry_price: Optional[float] = None
    exit_price: Optional[float] = None
    current_price: Optional[float] = None
    exit_reason: Optional[str] = None
    signal_type: Optional[str] = None  # TRADE, INTIMATION, UPDATE
    expiry_date: Optional[str] = None  # e.g., "18 SEP"
    update_type: Optional[str] = None  # For UPDATE signals like "STOP LOSS"
    new_stop_loss: Optional[float] = None  # For stop loss updates

class APIHandler:
    def __init__(self):
        self.logger = logger.get_logger('api')
        self.smart_api = self._init_smartapi()
        self.instrument_df = self._load_instruments()
        self.active_trades = {}
    
    def _init_smartapi(self) -> Optional[SmartConnect]:
        """Initialize SmartAPI client with enhanced error handling"""
        try:
            obj = SmartConnect(api_key=config.SMARTAPI_API_KEY)
            totp = pyotp.TOTP(config.SMARTAPI_TOTP_SECRET)
            data = obj.generateSession(
                config.SMARTAPI_USER_ID,
                config.SMARTAPI_PIN,
                totp.now()
            )
            
            if data['status']:
                self.logger.info("SmartAPI client initialized successfully")
                return obj
            else:
                self.logger.error(f"SmartAPI initialization failed: {data['message']}")
                return None
                
        except Exception as e:
            self.logger.error(f"SmartAPI initialization failed: {e}")
            return None
            
    def _get_latest_instrument_file(self) -> Optional[str]:
        """Get the latest instrument file from the Dependencies directory"""
        try:
            pattern = os.path.join(config.INSTRUMENT_FILE_PATH, "all_instrument*.csv")
            files = glob.glob(pattern)
            if not files:
                self.logger.error("No instrument files found")
                return None
            return max(files, key=os.path.getctime)
        except Exception as e:
            self.logger.error(f"Error finding instrument file: {e}")
            return None
            
    def _load_instruments(self) -> pd.DataFrame:
        """Load and cache instrument data"""
        try:
            filepath = self._get_latest_instrument_file()
            if not filepath:
                return pd.DataFrame()
                
            df = pd.read_csv(filepath)
            self.logger.info(f"Loaded {len(df)} instruments from {filepath}")
            return df
            
        except Exception as e:
            self.logger.error(f"Failed to load instruments: {e}")
            return pd.DataFrame()

    def find_instrument(self, signal: TradeSignal) -> Optional[Dict]:
        """Find instrument details from the loaded instrument data"""
        try:
            if signal.strike and signal.option_type and signal.expiry_date:
                # Option instrument - match using the actual data format
                # Convert expiry "16 SEP" to "16SEP2025" format
                expiry_parts = signal.expiry_date.split()
                if len(expiry_parts) == 2:
                    day, month = expiry_parts
                    # Assume current year + 1 for options (adjust as needed)
                    year = "2025"  # This should be dynamic based on actual expiry year
                    formatted_expiry = f"{day}{month}{year}"
                else:
                    formatted_expiry = signal.expiry_date

                # Convert strike to paise (multiply by 100)
                strike_in_paise = signal.strike * 100

                # Build expected trading symbol format
                expected_trading_symbol = f"{signal.symbol}{formatted_expiry}{int(signal.strike * 100)}{signal.option_type}"

                self.logger.info(f"Looking for instrument with trading symbol: {expected_trading_symbol}")

                # First try exact match on trading symbol
                mask = self.instrument_df['SEM_TRADING_SYMBOL'] == expected_trading_symbol
                matches = self.instrument_df[mask]

                if matches.empty:
                    # If no exact match, try component-wise matching
                    mask = (
                        (self.instrument_df['SEM_CUSTOM_SYMBOL'] == signal.symbol) &
                        (self.instrument_df['SEM_STRIKE_PRICE'] == strike_in_paise) &
                        (self.instrument_df['SEM_EXPIRY_DATE'].str.contains(formatted_expiry, na=False))
                    )
                    matches = self.instrument_df[mask]

                    # If still no match, try with option type conversion
                    if matches.empty:
                        option_type_in_symbol = "CE" if signal.option_type == "CE" else "PE"
                        mask = (
                            (self.instrument_df['SEM_CUSTOM_SYMBOL'] == signal.symbol) &
                            (self.instrument_df['SEM_STRIKE_PRICE'] == strike_in_paise) &
                            (self.instrument_df['SEM_TRADING_SYMBOL'].str.contains(option_type_in_symbol, na=False))
                        )
                        matches = self.instrument_df[mask]

            elif signal.strike and signal.option_type:
                # Option instrument without expiry date - match by symbol, strike, and option type
                mask = (
                    (self.instrument_df['SEM_CUSTOM_SYMBOL'].str.contains(signal.symbol, na=False)) &
                    (self.instrument_df['SEM_STRIKE_PRICE'] == signal.strike) &
                    (self.instrument_df['SEM_OPTION_TYPE'] == signal.option_type)
                )
                matches = self.instrument_df[mask]
            else:
                # Stock/Index instrument - match by symbol name
                mask = self.instrument_df['SEM_CUSTOM_SYMBOL'].str.contains(f"^{signal.symbol}$", na=False, regex=True)
                matches = self.instrument_df[mask]

            if not matches.empty:
                # Convert to the expected format for API calls
                instrument = matches.iloc[0]
                return {
                    'exchange': instrument['SEM_EXM_EXCH_ID'],
                    'tradingsymbol': instrument['SEM_TRADING_SYMBOL'],
                    'token': str(instrument['SEM_SMST_SECURITY_ID']),
                    'symbol': signal.symbol,
                    'strike': instrument.get('SEM_STRIKE_PRICE', 0),
                    'option_type': instrument.get('SEM_OPTION_TYPE', ''),
                    'custom_symbol': instrument['SEM_CUSTOM_SYMBOL']
                }
            else:
                self.logger.warning(f"No instrument found for signal: {signal}")
                return None

        except Exception as e:
            self.logger.error(f"Error finding instrument: {e}")
            return None

    def get_market_data(self, instrument: Dict) -> Optional[Dict]:
        """Get current market data for an instrument"""
        try:
            if self.smart_api:
                data = self.smart_api.ltpData(
                    exchange=instrument['exchange'],
                    tradingsymbol=instrument['tradingsymbol'],
                    symboltoken=instrument['token']
                )
                return data.get('data', {}) if data['status'] else None
            return None
            
        except Exception as e:
            self.logger.error(f"Error fetching market data: {e}")
            return None

    def process_signal(self, signal: TradeSignal) -> Dict[str, Any]:
        """Process incoming trade signal"""
        try:
            if not config.is_market_open():
                return {"status": "error", "message": "Market is closed"}
                
            if not self.smart_api:
                return {"status": "error", "message": "SmartAPI not initialized"}
                
            instrument = self.find_instrument(signal)
            if not instrument:
                return {"status": "error", "message": f"Instrument not found for {signal.symbol}"}
                
            market_data = self.get_market_data(instrument)
            if not market_data:
                return {"status": "error", "message": "Unable to fetch market data"}
                
            # Execute trade based on signal
            if signal.action.upper() in ['BUY', 'SELL']:
                return self._execute_trade(signal, instrument, market_data)
            elif signal.action.upper() == 'EXIT':
                return self._exit_position(signal.symbol)
            else:
                return {"status": "error", "message": f"Invalid action {signal.action}"}
                
        except Exception as e:
            self.logger.error(f"Signal processing failed: {e}")
            return {"status": "error", "message": str(e)}

    def _execute_trade(self, signal: TradeSignal, instrument: Dict, market_data: Dict) -> Dict[str, Any]:
        """Execute trade through SmartAPI"""
        try:
            order_params = {
                "variety": "NORMAL",
                "tradingsymbol": instrument['tradingsymbol'],
                "symboltoken": instrument['token'],
                "transactiontype": signal.action.upper(),
                "exchange": instrument['exchange'],
                "ordertype": "MARKET",
                "producttype": "INTRADAY",
                "duration": "DAY",
                "price": "0",
                "squareoff": "0",
                "stoploss": "0",
                "quantity": str(signal.quantity)
            }
            
            response = self.smart_api.placeOrder(order_params)
            
            if response.get('status'):
                order_id = response.get('data', {}).get('orderid')
                self.active_trades[signal.symbol] = {
                    "order_id": order_id,
                    "entry_time": datetime.now(),
                    "entry_price": market_data.get('ltp'),
                    "quantity": signal.quantity,
                    "action": signal.action,
                    "instrument": instrument
                }
                
                return {
                    "status": "success",
                    "message": "Order placed successfully",
                    "order_id": order_id,
                    "price": market_data.get('ltp')
                }
            else:
                return {"status": "error", "message": response.get('message', 'Order placement failed')}
                
        except Exception as e:
            self.logger.error(f"Trade execution failed: {e}")
            return {"status": "error", "message": str(e)}

    def _exit_position(self, symbol: str) -> Dict[str, Any]:
        """Exit an existing position"""
        try:
            if symbol not in self.active_trades:
                return {"status": "error", "message": f"No active position found for {symbol}"}
                
            position = self.active_trades[symbol]
            exit_action = "SELL" if position['action'] == "BUY" else "BUY"
            instrument = position['instrument']
            
            order_params = {
                "variety": "NORMAL",
                "tradingsymbol": instrument['tradingsymbol'],
                "symboltoken": instrument['token'],
                "transactiontype": exit_action,
                "exchange": instrument['exchange'],
                "ordertype": "MARKET",
                "producttype": "INTRADAY",
                "duration": "DAY",
                "price": "0",
                "squareoff": "0",
                "stoploss": "0",
                "quantity": str(position['quantity'])
            }
            
            response = self.smart_api.placeOrder(order_params)
            
            if response.get('status'):
                del self.active_trades[symbol]
                return {
                    "status": "success",
                    "message": "Position exited successfully",
                    "order_id": response.get('data', {}).get('orderid')
                }
            else:
                return {"status": "error", "message": response.get('message', 'Exit order failed')}
                
        except Exception as e:
            self.logger.error(f"Position exit failed: {e}")
            return {"status": "error", "message": str(e)}
